import dayjs from "dayjs";
import { useEffect, useState } from "react";

const Bi01Top = () => {
    const [currentTime, setCurrentTime] = useState(dayjs());

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(dayjs());
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    const weatherInfo = {
        temperature: '26℃',
        weather: '晴',
        humidity: '65%',
        windDirection: '东南风',
        windPower: '2级'
    };

    return (
        <div className="fixed left-0 top-0 w-full h-[110px] flex justify-between items-center px-12 -mt-3 text-cyan-100">
            <div className="flex items-center gap-4  w-[500px]">
                <div className="text-4xl font-bold">
                    {currentTime.format('HH:mm:ss')}
                </div>
                <div className="text-sm leading-5">
                    <div>{currentTime.format('YYYY年MM月DD日')}</div>
                    <div>{currentTime.format('dddd')}</div>
                </div>
            </div>
            <div className="font-bold text-[40px] drop-shadow-md">
                斗南花市大数据可视化平台
            </div>
            <div className=" flex items-center gap-4 flex-row-reverse w-[540px]">
                <div className="text-4xl font-bold">
                    {weatherInfo.temperature}
                </div>
                <div className="text-sm leading-5 text-right ">
                    <div>{weatherInfo.weather} {weatherInfo.windDirection}</div>
                    <div>湿度 {weatherInfo.humidity} {weatherInfo.windPower}</div>
                </div>
            </div>
        </div>
    );
}

export default Bi01Top;