import { CopyOutlined } from "@ant-design/icons";
import { App, Dropdown, Tag } from "antd";
import { useMemo } from "react";

export interface TOcrBusinessLicenseProps {
  value?: string;
}

function OcrBusinessLicense(props: TOcrBusinessLicenseProps) {
  const { message } = App.useApp();
  // {
  //   "data": {
  //     "title": "营业执照",
  //     "creditCode": "92530121MA6L4CHD63",
  //     "companyName": "呈贡县斗南镇蓝月亮花卉经营部",
  //     "companyType": "个体工商户",
  //     "businessAddress": "云南省昆明市呈贡区斗南街道斗南花卉市场内",
  //     "legalPerson": "蓝海儒",
  //     "businessScope": "花卉种植销售;工艺品的销售。(依法须经批准的项目,经相关部门批准后方可开展经营活动)",
  //     "registeredCapital": "",
  //     "RegistrationDate": "2004年10月15日",
  //     "validPeriod": "",
  //     "validFromDate": "20041015",
  //     "validToDate": "",
  //     "issueDate": "2019年6月28日",
  //     "companyForm": "个人经营"
  //   }
  // }

  const data = useMemo(() => {
    if (!props.value) return null;
    try {
      return JSON.parse(props.value)?.data;
    } catch (error) {
      console.error('解析营业执照OCR结果失败:', error);
      return null;
    }
  }, [props.value]);
  return (
    <div className="OcrBusinessLicense">
      {data ? (
        <Dropdown menu={{
          items: [
            { icon: <CopyOutlined />, label: `企业形式: ${data.companyForm || '一般企业'}`, key: 'companyForm' },
            { icon: <CopyOutlined />, label: `名称: ${data.companyName}`, key: 'companyName' },
            { icon: <CopyOutlined />, label: `信用代码: ${data.creditCode}`, key: 'creditCode' },
            { icon: <CopyOutlined />, label: `法人代表: ${data.legalPerson}`, key: 'legalPerson' },
            { icon: <CopyOutlined />, label: `地址: ${data.businessAddress}`, key: 'businessAddress' },
            // { icon: <CopyOutlined />, label: `经营范围: ${data.businessScope}`, key: 'businessScope' },
            // { icon: <CopyOutlined />, label: `注册资金: ${data.registeredCapital}`, key: 'registeredCapital' },
            // { icon: <CopyOutlined />, label: `注册日期: ${data.registrationDate}`, key: 'registrationDate' },
            // { icon: <CopyOutlined />, label: `有效期: ${data.validPeriod}`, key: 'validPeriod' },
            // { icon: <CopyOutlined />, label: `有效期开始日期: ${data.validFromDate}`, key: 'validFromDate' },
            // { icon: <CopyOutlined />, label: `有效期结束日期: ${data.validToDate}`, key: 'validToDate' },
            // { icon: <CopyOutlined />, label: `发证日期: ${data.issueDate}`, key: 'issueDate' },
            
          ],
          onClick: (item) => {
            navigator.clipboard.writeText(data[item.key])
            message.success('复制成功')
          }
        }}
          arrow
        >
          <div className="cursor-pointer">
            <Tag color={data.companyForm === '个人经营' ? 'green' : 'blue'} >{data.companyForm || '一般企业'}</Tag><span className="text-blue-500">{data.companyName}</span>
          </div>
        </Dropdown>
      ) : (
        null
      )}
    </div>
  )
}

export default OcrBusinessLicense
