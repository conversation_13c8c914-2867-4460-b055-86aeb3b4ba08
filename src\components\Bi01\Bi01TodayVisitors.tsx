// import { visitorsModel } from "@/stores/visitorsModel";
import BiStatisticsNum3 from "../BiStatisticsNum3";
import { useCreation, useRequest } from "ahooks";
// import dayjs from "dayjs";
// import { useModel } from "foca";
// import { ToolsUtil } from "@/utils/tools";
import { useStepedRequest } from "@/hooks/useStepedRequest";
import { apiBiPeopleCountRegionInfo } from "@/apis/apis.api";
import { useModel } from "foca";
import { settingModel } from "@/stores/settingModel";


function Bi01TodayVisitors() {

  const settingModelState = useModel(settingModel);
  const peopleCountRate = useCreation(() => settingModelState?.Admin?.peopleCountRate || 1, [settingModelState?.Admin?.peopleCountRate]);
  // refreshDeps: [peopleCountRate]

  const apiBiPeopleCountRegionInfoSteped = useStepedRequest(apiBiPeopleCountRegionInfo);

  const { data: visitorCount } = useRequest(async () => {
    const res = await apiBiPeopleCountRegionInfoSteped()
    return Math.round(res.enterNumber * peopleCountRate)
  }, {
    pollingInterval: 60 * 1000,
    refreshDeps: [peopleCountRate]
  })

  return <div className="w-full h-full flex items-center justify-center">
    <BiStatisticsNum3 value={visitorCount || 0} len={5} bgColor="#193E36" textColor="#c9e8f2" />
    <div className="text-cyan-100 ml-4 h-[100px] flex items-end text-6">人</div>
  </div>;
}

export default Bi01TodayVisitors;
