import { useAsyncEffect, useCreation, useSet } from "ahooks";
import BiAutoScrollView, { TBiAutoScrollViewRef } from "../BiAutoScrollView";
import NumUtils from "@/utils/num";
import BiStatisticsNum3 from "../BiStatisticsNum3";
import dayjs from "dayjs";
import { useEffect, useRef, useState } from "react";
import useWebSocket, { ReadyState } from 'react-use-websocket';
import { biTradingModel } from "@/stores/biTradingModel";
import { useModel } from "foca";
import { apiBiTradingLatestCount } from "@/apis/apis.api";

type TOrder = {
    order_id: number,
    group_order_id: number,
    store_name: string,
    order_sn: string,
    real_name: string,
    user_address: string,
    total_num: number,
    pay_price: string,
    create_time: string,
    pay_time: string
}

const Bi01TradingTrend1 = () => {
    const biTradingModelState = useModel(biTradingModel)
    const scrollViewRef = useRef<TBiAutoScrollViewRef>(null)
    const [orderList, setOrderList] = useState<TOrder[]>([])
    const [orderTotalBefore, setOrderTotalBefore] = useState(0)
    const [orderTotalToday, setOrderTotalToday] = useState(0)
    const [orderTotalWs, setOrderTotalWs] = useState(0)
    const [amountTotalBefore, setAmountTotalBefore] = useState(0)
    const [amountTotalToday, setAmountTotalToday] = useState(0)
    const [amountTotalWs, setAmountTotalWs] = useState(0)

    const heartbeatTimerRef = useRef<any>()

    const [newOrderSnSet, { add: addNewOrderSn, remove: removeNewOrderSn }] = useSet<string>()

    const {
        sendMessage,
        sendJsonMessage,
        lastJsonMessage,
        readyState,
    } = useWebSocket<any>('wss://dc.dounanyun.com/ssl', {
        onOpen: () => {
            if (heartbeatTimerRef.current) {
                clearInterval(heartbeatTimerRef.current)
            }
            const sendHeartbeat = () => {
                sendMessage('ping')
            };
            heartbeatTimerRef.current = setInterval(sendHeartbeat, 20 * 1000)
            sendHeartbeat()
        },
        onClose: () => {
            if (heartbeatTimerRef.current) {
                clearInterval(heartbeatTimerRef.current)
            }
        },
        shouldReconnect: () => true,
        reconnectAttempts: 100000,
        reconnectInterval: (lastAttemptNumber: number) => {
            return lastAttemptNumber < 20 ? 5 * 1000 : 60 * 1000
        },
        heartbeat: false,
    })

    useEffect(() => {
        // console.log(111, ReadyState.OPEN)
        if (readyState === ReadyState.OPEN) {
            // console.log('readyState', readyState ,dayjs().format('YYYY-MM-DD HH:mm:ss'))
            sendJsonMessage({
                "pathInfo": "worker/OrderGround/liveOrder",
                "ba-user-token": biTradingModelState.token
            })
        }
    }, [readyState, biTradingModelState.token])

    useEffect(() => {
        if (lastJsonMessage && typeof lastJsonMessage === 'object' && lastJsonMessage?.order_sn) {
            const newOrders: TOrder[] = [lastJsonMessage]
            setOrderList(orderList => [...newOrders, ...orderList].slice(0, 100))
            setOrderTotalWs(base => base + (newOrders.length || 0))
            setAmountTotalWs(base => base + newOrders.reduce((pre, cur) => pre + Number(cur.pay_price), 0))
            newOrders.forEach(item => {
                addNewOrderSn(item.order_sn)
                setTimeout(() => {
                    removeNewOrderSn(item.order_sn)
                }, 3000)
            })
        }
    }, [lastJsonMessage])

    useEffect(() => {
        scrollViewRef.current.toTop()
    }, [orderList])

    useAsyncEffect(async () => {
        const today = dayjs().format('YYYY-MM-DD')
        const tomorrow = dayjs().add(1, 'day').format('YYYY-MM-DD')
        const response = await fetch(`https://dc.dounanyun.com/api/screen/bydate?date=${today},${tomorrow}&limit=100`, {
            headers: {
                "server": "1",
                "ba-user-token": biTradingModelState.token,
            }
        });
        const res = await response.json() as {
            code: number,
            msg: string,
            data: {
                list: TOrder[],
                count: number;
                page: number;
                total_payment: string;
            }
        };
        if (res.code !== 1) {
            console.log(res.msg)
            return
        }
        setOrderList(res.data.list.slice(0, 100))
        setOrderTotalToday(res.data.count)
        setOrderTotalWs(0)
        setAmountTotalToday(Number(res.data.total_payment))
        setAmountTotalWs(0)

    }, [biTradingModelState.token])

    useAsyncEffect(async () => {
        // const yesterday = dayjs().format('YYYY-MM-DD')
        // const response = await fetch(`https://dc.dounanyun.com/api/screen/bydate?date=1970-01-01,${yesterday}&limit=1`, {
        //     headers: {
        //         "server": "1",
        //         "ba-user-token": biTradingModelState.token,
        //     }
        // });
        // const res = await response.json() as {
        //     code: number,
        //     msg: string,
        //     data: {
        //         list: TOrder[],
        //         count: number;
        //         page: number;
        //         total_payment: string;
        //     }
        // };
        // if (res.code !== 1) {
        //     console.log(res.msg)
        //     return
        // }
        // // const res = await apiBiTradingLatestCount({ type: 1 })
        // setOrderTotalBefore(res.data.count)
        // setAmountTotalBefore(Number(res.data.total_payment))
        const res = await apiBiTradingLatestCount({ type: 1 })
        setOrderTotalBefore(res.orderCount)
        setAmountTotalBefore(res.amountCount)
    }, [])

    const orderTotal = useCreation(() => {
        return orderTotalBefore + orderTotalToday + orderTotalWs
    }, [orderTotalBefore, orderTotalToday, orderTotalWs])

    const amountTotal = useCreation(() => {
        // console.log('打开页面当天之前', amountTotalBefore,
        //     '\n打开页面当天', amountTotalToday,
        //     '\n打开页面后实时累计', amountTotalWs,
        //     '\n总金额', amountTotalBefore + amountTotalToday + amountTotalWs)

        return Math.round(amountTotalBefore + amountTotalToday + amountTotalWs)
    }, [amountTotalBefore, amountTotalToday, amountTotalWs])

    useEffect(() => {
        biTradingModel.setState({
            trading1TodayAmountTotal: amountTotal
        })
    }, [amountTotal])

    const data = useCreation(() => {
        return orderList.map(item => {
            return {
                key: item.order_sn,
                id: item.order_sn,
                title: item.store_name,
                user: `${(item.real_name || '').slice(0, 1) + '**'} ${(item.user_address || '').slice(0, 4) + '**'}`,
                moneyCent: Math.round(Number(item.pay_price) * 100),
                createTime: item.create_time
            }
        })
    }, [orderList])


    return (
        <div className="relative w-full flex flex-col" >
            <div className="mb-4 flex flex-col gap-1">

                <div className="flex">
                    <div className="w-140px shrink-0 text-cyan-100">累计订单数(单)</div>
                    <BiStatisticsNum3 value={orderTotal || 0} size={14} showSplit />
                </div>
                <div className="flex">
                    <div className="w-140px shrink-0 text-cyan-100">累计成交金额(元)</div>
                    <BiStatisticsNum3 value={amountTotal || 0} size={14} showSplit />
                </div>

                {/* <BiStatisticsNum3 value={orderTotal || 0} size={16} label="累计订单数(单)" showSplit />
                <BiStatisticsNum3 value={amountTotal || 0} size={16} label="累计成交金额(元)" showSplit /> */}
            </div>
            <BiAutoScrollView itemHeight={70} height={240} delay={3000} ref={scrollViewRef}>
                {(data || []).map((item, index) => (
                    <div key={index}
                        className='relative h-60px w-full mb-10px text-white px-10px  rounded-md flex flex-col justify-center'
                        style={{
                            transition: 'all 1s ease-in-out',
                            background: newOrderSnSet.has(item.key) ? '#FDBF4433' : '#15E1FD22',
                        }}
                    >
                        <div className='absolute left-0 top-1/2 -translate-y-1/2 w-2px h-20px bg-green-500'></div>
                        <div className="flex flex-col gap-0.5  text-[10px] text-[#ffffff55]">
                            <div>{item.id}</div>
                            <div className='text-[#ffffff88] text-xs'>{item.title}</div>
                            <div className="flex justify-between items-center">
                                <span>{item.user}</span>
                                <span>{item.createTime}</span>
                            </div>
                        </div>
                        <div className='text-cyan-500 absolute right-10px top-10px'>¥ {NumUtils.moneyCentFormat(item.moneyCent, '')}</div>
                    </div>
                ))}
            </BiAutoScrollView>
        </div>
    );
};

export default Bi01TradingTrend1;