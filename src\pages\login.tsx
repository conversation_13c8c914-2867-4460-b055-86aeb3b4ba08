import { useRequest } from 'ahooks';
import { authModel } from '@/stores/authModel';
import { useNavigate } from 'react-router-dom';
import { SchemaField, useForm } from '@/components/Formily';
import { FormProvider } from '@formily/react';
import { FormLayout, Submit } from '@formily/antd-v5';

type TLoginFormData = {
  username: string;
  password: string;
}

function LoginPage() {
  const navigate = useNavigate();
  const form = useForm<TLoginFormData>();

  const { loading, runAsync: submit } = useRequest(async (values: TLoginFormData) => {
    await authModel.loginByPwd(values)
    navigate('/')
  }, { manual: true })

  return (
    <div className="flex items-center justify-center w-full h-screen LoginPage bg-slate-800">
      <div className="flex flex-col p-8 bg-white rounded-lg w-[300px]">
        <div className='flex flex-col items-center'>
          <div className='py-6 text-xl font-bold'>{import.meta.env.VITE_APP_TITLE}</div>
        </div>
        <FormProvider form={form}>
          <FormLayout layout="vertical">
            <SchemaField>
              <SchemaField.String
                name="username"
                title="用户"
                x-decorator="FormItem"
                x-component="Input"
                required
              />
              <SchemaField.String
                name="password"
                title="密码"
                x-decorator="FormItem"
                x-component="Input"
                required
                x-component-props={{
                  type: 'password',
                }}
              />
            </SchemaField>
          </FormLayout>

          <Submit className="w-full mt-2" onSubmit={submit} loading={loading}>登录</Submit>
        </FormProvider>
      </div>
    </div>
  );
}

export default LoginPage;

LoginPage.auth = [

]