import './BiChartCard.less';

export interface TBiChartCardProps {
  width: number;
  height: number;
  title?: string;
  children?: React.ReactNode;
  left?: number;
  top?: number;
  zIndex?: number;
  hideTitle?: boolean;
  titleWidth?: number;
  padding?: string;
}

function BiChartCard(props: TBiChartCardProps) {

  return (
    <div className="BiChartCard fixed flex flex-col" style={{ width: props.width, height: props.height, left: props.left, top: props.top, zIndex: props.zIndex }}>
      {props.title && !props.hideTitle ? <div className="shrink-0 w-full relative h-[45px]">
        <div className="flex absolute left-0 top-0 w-full h-full">
          <div className="shrink-0 w-[190px] h-full" style={{ backgroundImage: 'url(/images/bi/title_left.png)', backgroundSize: '100% 100%' }}></div>
          <div className="flex-1 h-full" style={{ backgroundImage: 'url(/images/bi/title_center.png)', backgroundSize: '100% 100%' }}></div>
          <div className="shrink-0 w-[40px] h-full" style={{ backgroundImage: 'url(/images/bi/title_right.png)', backgroundSize: '100% 100%' }}></div>
        </div>

        <div className={`absolute left-[110px] -top-1 h-full flex flex-col justify-center font-bold text-[#91e4ff] text-4 `} style={{ width: props.titleWidth, overflow: props.titleWidth ? 'hidden' : 'visible' }}>
          <div className={`${props.titleWidth ? 'animate-title' : ''}`}>{props.title}</div>
        </div>
      </div> : null}
      <div className="flex-1" style={{border: '1px solid #ffffff00', boxSizing: 'border-box', padding: props.padding || '16px'}}>{props.children}</div>
    </div>
  )
}

export default BiChartCard
