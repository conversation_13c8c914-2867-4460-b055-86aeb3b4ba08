import { apiBiPeopleCountInAndOutReport } from "@/apis/apis.api";
import { useEcharts } from "@/hooks/useEcharts";
import { useStepedRequest } from "@/hooks/useStepedRequest";
import { settingModel } from "@/stores/settingModel";
import { useCreation, useRequest } from "ahooks"
import dayjs from "dayjs";
import { useModel } from "foca";
import { useEffect } from "react";

const Bi01MonthlyTrend = () => {
    
    const settingModelState = useModel(settingModel);
    const peopleCountRate = useCreation(() => settingModelState?.Admin?.peopleCountRate || 1, [settingModelState?.Admin?.peopleCountRate]);
    // refreshDeps: [peopleCountRate]
    
    const apiBiPeopleCountInAndOutReportSteped = useStepedRequest(apiBiPeopleCountInAndOutReport);

    const { data } = useRequest(async () => {
        const start = dayjs().subtract(30, 'day').format('YYYY-MM-DD');
        const end = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
        const res = await apiBiPeopleCountInAndOutReportSteped({
            timeUnit: 'day',
            start: start,
            end: end
        })
        let list = [];
        for (let i = 0; i < res.in.length; i++) {
           
            for (let j = 0; j < res.in[i].data.length; j++) {
                const date = dayjs(res.in[i].data[j].showTime).format('MM-DD');
                const inCount = Math.round((res.in[i].data[j].remainder || 0) * peopleCountRate);
                const outCount = Math.round((res.out[i].data[j].remainder || 0) * peopleCountRate);
                list.push({
                    date,
                    inCount ,
                    outCount
                })
            }
        }        
        return list
    }, {
        pollingInterval: 60 * 60 * 1000,
        refreshDeps: [peopleCountRate]
    })

    const { chart, setContainerRef } = useEcharts();

    useEffect(() => {
        if (chart && data) {
            const xData = data.map((item) => item.date);
            const inData = data.map((item) => item.inCount);
            const outData = data.map((item) => item.outCount);

            chart.setOption({
                grid: {
                    left: 0,
                    right: 60,
                    bottom: 0,
                    top: 40,
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    // top: 10,
                    // right: 20,
                    textStyle: {
                        color: '#C9E8F2',
                        fontSize: 12
                    },
                    itemWidth: 12,
                    itemHeight: 12,
                    data: ['入场人数', '出场人数']
                },
                xAxis: {
                    name: '日期',
                    nameTextStyle: {
                        color: '#02CABB',
                        align: 'left'
                    },
                    type: 'category',
                    data: xData,
                    axisTick: {
                        show: false,
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#C9E8F2'
                        }
                    },
                },
                yAxis: {
                    name: '人数(人)',
                    nameTextStyle: {
                        color: '#02CABB',
                    },
                    type: 'value',
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#ffffff10'
                        }
                    }
                },
                series: [
                    {
                        name: '入场人数',
                        type: 'bar',
                        barWidth: 12,
                        data: inData,
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 1,
                                x2: 0,
                                y2: 0,
                                colorStops: [{
                                    offset: 0,
                                    color: '#00D5FF00'
                                }, {
                                    offset: 0.9,
                                    color: '#00D5FF'
                                },
                                {
                                    offset: 1,
                                    color: '#C7F6FF'
                                }]
                            }
                        }
                    },
                    {
                        name: '出场人数',
                        type: 'bar',
                        barWidth: 12,
                        data: outData,
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 1,
                                x2: 0,
                                y2: 0,
                                colorStops: [{
                                    offset: 0,
                                    color: '#00B36500'
                                }, {
                                    offset: 0.9,
                                    color: '#00B365'
                                },
                                {
                                    offset: 1,
                                    color: '#BEEBD8'
                                }]
                            }
                        }
                    }
                ]
            });
        }
    }, [chart, data]);

    return <div ref={setContainerRef} className="w-full h-full"></div>;
}

export default Bi01MonthlyTrend;
