/**
 * 增加组件
 * 用法：`pnpm genp -n user/list`添加一个`user/list`页面到`src/pages/user/list`
 */
import { program } from 'commander';
import fs from 'fs';
import _ from 'lodash';

program.name('pnpm genp').option('-n, --name <string>', '设置页面名称').helpOption('-h, --help', '打印帮助信息');

program.parse();

const { name } = program.opts();

const directory = name.split('/').slice(0, -1).join('/');
const pageName = _.upperFirst(_.camelCase(name.split('/').pop()));
fs.mkdirSync(`src/pages/${directory}`, { recursive: true });
fs.writeFileSync(
  `src/pages/${name}.tsx`,
  `
function ${pageName}() {
  return (
    <div>
      ${pageName}
    </div>
  );
}

export default ${pageName};

${pageName}.auth = [
  
] as string[];
`
);

/** 修改router */
let routerContent = fs.readFileSync('src/router.tsx', 'utf-8');
routerContent = routerContent.replace(/(\s+)(\/\/__gen1__)/g, `$1import ${pageName} from '@/pages/${name}.tsx';$1$2`);
routerContent = routerContent.replace(
  /(\s+)(\/\/__gen2__)/g,
  `$1{$1  path: '/${name}',$1  name: '${pageName}',$1  icon: <HomeOutlined />,$1  component: <${pageName} />,$1},$1$2`
);
// console.log(routerContent);
fs.writeFileSync('src/router.tsx', routerContent);

console.log(`页面已创建，请修改页面内容\nsrc/pages/${name}.tsx`);
