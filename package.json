{"name": "dnhhsj-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"page": "tsx ./bin/genPage.ts -n", "comp": "tsx ./bin/genComponent.ts -n", "apis": "tsx ./bin/genApis.ts", "dev": "vite --mode debug", "up-prod": "vite build --mode prod && ssh-upload prod", "up-dev": "vite build --mode dev && ssh-upload dev", "analyze": "vite build --mode analyze", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@ant-design/pro-components": "2.7.14", "@antv/l7": "^2.22.3", "@antv/l7-maps": "^2.22.3", "@babel/parser": "^7.26.2", "@babel/traverse": "^7.25.9", "@formily/antd-v5": "^1.2.2", "@formily/core": "^2.3.2", "@formily/react": "^2.3.2", "@formily/reactive": "^2.3.2", "@types/babel__traverse": "^7.20.6", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "@zip.js/zip.js": "^2.7.52", "ahooks": "^3.8.0", "animejs": "^3.2.2", "antd": "5.19.3", "array-to-tree": "^3.3.2", "async-wait-until": "^2.0.12", "axios": "^1.7.2", "bsonid": "^1.0.0", "clsx": "^2.1.1", "dayjs": "^1.11.12", "default-passive-events": "^2.0.0", "district-data": "^0.0.17", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "eventbus-z": "^2.0.2", "exceljs": "^4.4.0", "fabric": "^6.4.2", "fflate": "^0.8.2", "foca": "^3.2.0", "g": "^2.0.1", "hls.js": "^1.5.17", "immer": "^10.1.1", "kaplay": "3001.1.0-beta.0", "lodash": "^4.17.21", "nanoid": "^3.3.7", "number-precision": "^1.6.0", "pdf-lib": "^1.17.1", "pretty-bytes": "^6.1.1", "qrcode": "^1.5.4", "qrcode-svg": "^1.1.0", "qs": "^6.12.3", "rc-field-form": "^1.22.0", "react": "~18.2.0", "react-activation": "^0.13.0", "react-dom": "~18.2.0", "react-player": "^2.16.0", "react-router-dom": "^6.25.1", "react-use-websocket": "^4.11.1", "swiper": "^11.1.15", "tony-mockjs": "^1.1.4", "url-parse": "^1.5.10", "uuid": "^10.0.0", "zustand": "^5.0.1"}, "devDependencies": {"@types/animejs": "^3.1.12", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.17.7", "@types/node": "^20.14.12", "@types/qrcode": "^1.5.5", "@types/qrcode-svg": "^1.1.5", "@types/qs": "^6.9.15", "@types/react": "~18.2.0", "@types/react-dom": "~18.2.0", "@types/url-parse": "^1.4.11", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-swc": "^3.7.0", "autoprefixer": "^10.4.19", "commander": "^12.1.0", "eslint": "^9.7.0", "eslint-plugin-react": "^7.35.0", "js-yaml": "^4.1.0", "less": "^4.2.0", "postcss": "^8.4.40", "prettier": "^3.3.3", "sass": "^1.77.8", "snorun": "^1.4.0", "typescript": "^5.5.4", "unocss": "^0.61.5", "vite": "^5.3.5", "vite-bundle-analyzer": "^0.10.6", "vite-plugin-externals": "^0.6.2", "vue-tsc": "^2.0.29"}}