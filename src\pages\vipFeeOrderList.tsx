import { apiVipFeeOrderPageList } from "@/apis/apis";
import UserObj from "@/components/UserObj";
import { AgreeDicts } from "@/utils/agreeDict";
import { ToolsUtil } from "@/utils/tools";
import { ActionType, ProTable } from "@ant-design/pro-components";
import { Space } from "antd";
import { useRef } from "react";
import { useNavigate } from "react-router-dom";

function VipFeeOrderList() {
  const tableRef = useRef<ActionType>();
  const navigate = useNavigate();

  return (
    <div>
      <ProTable<TVipFeeOrder>
        headerTitle="会员费订单列表"
        actionRef={tableRef}
        rowKey="id"
        search={{
          defaultCollapsed: false,
        }}
        pagination={{
          pageSize: 20,
          showQuickJumper: true,
        }}
        columns={[
          { dataIndex: 'id', title: 'ID' },
          { dataIndex: 'clientUserKeyword', title: '客户', render: (_text, record) => <UserObj userObj={record.clientUser} /> },
          { dataIndex: 'vipFeeTypeName', title: '会员费类型' },
          { 
            dataIndex: 'status', 
            title: '状态', 
            valueType: 'select',
            valueEnum: AgreeDicts.VipFeeOrder_status.valueLabelMap,
          },
          { dataIndex: 'amountCent', title: '金额', search: false, render: (text: number) => ToolsUtil.moneyCentFormat(text) },
          { dataIndex: 'vipFeeTypeDays', title: '有效天数', search: false },
          { 
            dataIndex: 'startTime', 
            title: '作用开始', 
            search: false,
            valueType: 'dateTime'
          },
          { 
            dataIndex: 'endTime', 
            title: '作用结束', 
            search: false,
           valueType: 'dateTime'
          },
          { 
            dataIndex: 'createdAt', 
            title: '创建时间', 
            search: false,
            valueType: 'dateTime'
          },
          {
            dataIndex: 'action', 
            title: '操作', 
            search: false,  
            width: 120, 
            render: (_, record) => (
              <Space>
                <a onClick={() => navigate(`/vipFeeOrderDetail?id=${record.id}`)}>查看</a>
              </Space>
            )
          },
        ]}
        request={async (params) => {
          const res = await apiVipFeeOrderPageList({
            pageIndex: params.current - 1,
            pageSize: params.pageSize,
            id: params.id,
            clientUserId: params.clientUserId,
            clientUserKeyword: params.clientUserKeyword,
            vipFeeTypeId: params.vipFeeTypeId,
            status: params.status,
          });
          return {
            data: res.rows,
            total: res.totalCount,
            success: true,
          };
        }}
      />
    </div>
  );
}

export default VipFeeOrderList;
