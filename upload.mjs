import { NodeSSH } from 'node-ssh';
import path from 'path';
import fs from 'fs-extra';
import os from 'os';

// 配置
const CONFIG = {
  REMOTE_DIR: '/www/flower-price-admin/',
  REMOTE_HOST: '*********',
  REMOTE_PORT: 22,
  REMOTE_USER: 'root',
  // 从项目根目录开始的相对路径
  LOCAL_DIR: './dist',
  // 排除的文件或目录
  EXCLUDE: ['.DS_Store', '.git', 'node_modules'],
};

// 创建 SSH 连接
const ssh = new NodeSSH();

// 获取所有文件路径
async function getFiles(dir) {
  const dirents = await fs.readdir(dir, { withFileTypes: true });
  const files = await Promise.all(
    dirents
      .filter((dirent) => !CONFIG.EXCLUDE.includes(dirent.name))
      .map((dirent) => {
        const res = path.resolve(dir, dirent.name);
        return dirent.isDirectory() ? getFiles(res) : res;
      })
  );
  return files.flat();
}

// 上传文件
async function uploadFiles() {
  try {
    console.log('正在连接到服务器...');
    
    // 连接到服务器
    await ssh.connect({
      host: CONFIG.REMOTE_HOST,
      port: CONFIG.REMOTE_PORT,
      username: CONFIG.REMOTE_USER,
      // 使用密码或私钥认证（二选一）
      // password: 'your-password',
      privateKey: fs.readFileSync(path.join(os.homedir(), '.ssh', 'id_rsa'), 'utf8'),
    });

    console.log('连接成功，开始上传文件...');
    
    // 确保远程目录存在
    await ssh.execCommand(`mkdir -p ${CONFIG.REMOTE_DIR}`);
    
    // 获取所有文件
    const files = await getFiles(CONFIG.LOCAL_DIR);
    
    // 上传每个文件
    for (const file of files) {
      const relativePath = path.relative(CONFIG.LOCAL_DIR, file);
      const remotePath = path.join(CONFIG.REMOTE_DIR, relativePath).replace(/\\/g, '/');
      const remoteDir = path.dirname(remotePath);
      
      // 确保远程目录存在
      await ssh.execCommand(`mkdir -p ${remoteDir}`);
      
      // 上传文件
      await ssh.putFile(file, remotePath);
      console.log(`已上传: ${relativePath}`);
    }
    
    console.log('\n所有文件上传完成!');
    
  } catch (error) {
    console.error('上传过程中出错:', error);
    process.exit(1);
  } finally {
    // 关闭连接
    ssh.dispose();
  }
}

// 执行上传
uploadFiles();
