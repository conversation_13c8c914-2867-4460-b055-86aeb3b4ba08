import { apiDashboardLcPark } from "@/apis/apis.api";
import { useEcharts } from "@/hooks/useEcharts";
import { ToolsUtil } from "@/utils/tools";
import { ProCard } from "@ant-design/pro-components";
import { useRequest } from "ahooks";
import dayjs from "dayjs";
import { useEffect } from "react";

type ParkChartProps = {

};

export const ParkChart = ({ }: ParkChartProps) => {

  const { data } = useRequest(async () => {
    const res = await apiDashboardLcPark({
      dateRange: [
        dayjs().subtract(60, 'minutes').valueOf(),
        dayjs().valueOf(),
      ]
    })
    return res
  }, {
    pollingInterval: 60 * 1000,
  })


  const { echarts, chart, setContainerRef } = useEcharts({
    theme: 'light',
  });

  useEffect(() => {
    if (chart && data) {
      const xAxisData = data.map((item) => ToolsUtil.timeFormat(item.minutes * 1000 * 60, {
        format: 'HH:mm'
      }));
      const yAxisData = data.map((item) => item.inParkCount);

      chart.setOption({
        grid: {
          left: 40,
          right: 40,
          bottom: 40,
          top: 10,
        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          boundaryGap: [0, '100%']
        },
        series: [
          {
            name: '在库车辆',
            type: 'line',
            symbol: 'none',
            sampling: 'lttb',
            itemStyle: {
              color: '#9FE080'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgb( 158,255, 68)'
                },
                {
                  offset: 1,
                  color: '#9FE080'
                }
              ])
            },
            data: yAxisData
          }
        ]
      })
    }
  }, [chart, data])

  return (
    <ProCard title="实时在库车辆" bordered className="flex-1">
      <div className="h-300px" ref={setContainerRef}>
      </div>
    </ProCard>

  );
}
