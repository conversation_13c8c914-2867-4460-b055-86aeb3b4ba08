import { useEffect, useState } from 'react';
import { useAsyncEffect, useCreation, useRequest } from 'ahooks';
import { useEcharts } from '@/hooks/useEcharts';
import BiStatisticsNum3 from '../BiStatisticsNum3';
import { useModel } from 'foca';
import { biTradingModel } from '@/stores/biTradingModel';
import { settingModel } from '@/stores/settingModel';
import dayjs from 'dayjs';

const token = "34dec3d76836c0b1f0125eaccc7aec3e"

const Bi01Building = () => {

  const biTradingModelState = useModel(biTradingModel);
  const settingModelState = useModel(settingModel);
  const [trading1MonthBeforeTodayAmountTotal, setTrading1MonthBeforeTodayAmountTotal] = useState(0);
  const [trading2BeforeMonthAmountTotal, setTrading2BeforeMonthAmountTotal] = useState(0);

  useAsyncEffect(async () => {
    if (!biTradingModelState.token || !settingModelState?.Admin?.beforeTradingAmountDate) return;
    const endDate = dayjs().format('YYYY-MM-DD')
    const startDate = settingModelState.Admin.beforeTradingAmountDate || dayjs().startOf('month').format('YYYY-MM-DD')
    const response = await fetch(`https://dc.dounanyun.com/api/screen/bydate?date=${startDate},${endDate}`, {
      headers: {
        "server": "1",
        "ba-user-token": biTradingModelState.token,
      }
    });
    const res = await response.json() as {
      code: number,
      msg: string,
      data: {
        total_payment: string;
      }
    };
    if (res.code !== 1) {
      console.log(res.msg)
      return
    }
    setTrading1MonthBeforeTodayAmountTotal(Number(res.data.total_payment))
  }, [biTradingModelState.token, settingModelState?.Admin?.beforeTradingAmountDate]);


  useAsyncEffect(async () => {
    if(!settingModelState?.Admin?.beforeTradingAmountDate) return;
    const endDate = settingModelState?.Admin?.beforeTradingAmountDate || dayjs().startOf('month').format('YYYY-MM-DD')
    const startDate = '1970-01-01'
    const response = await fetch('https://m.huashangpay.cn/ex/screen/api/getOrderStatistics', {
      method: 'POST',
      body: JSON.stringify({ 
        token,
        start_date: startDate,
        end_date: endDate
       }),
      headers: {
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    // console.log('getOrderStatistics', data);
    setTrading2BeforeMonthAmountTotal(data.data.total_price);
  }, [settingModelState?.Admin?.beforeTradingAmountDate]);
  // getOrderstatistics

  const beforeMonthTradingTotal = useCreation(() => {
    return settingModelState?.Admin?.beforeTradingAmountTotal ? Number(settingModelState?.Admin?.beforeTradingAmountTotal) : 0;
  }, [settingModelState?.Admin?.beforeTradingAmountTotal]);

  const thisMonthTradingTotal1 = useCreation(() => {
    return (trading1MonthBeforeTodayAmountTotal || 0) + (biTradingModelState.trading1TodayAmountTotal || 0);
  }, [trading1MonthBeforeTodayAmountTotal, biTradingModelState.trading1TodayAmountTotal]);

  const thisMonthTradingTotal2 = useCreation(() => {
    return (biTradingModelState.trading2AmountTotal || 0) - (trading2BeforeMonthAmountTotal || 0);
  }, [trading2BeforeMonthAmountTotal, biTradingModelState.trading2AmountTotal]);

  const tradingTotal = useCreation(() => {
    // console.log('beforeMonthTradingTotal', beforeMonthTradingTotal);
    // console.log('thisMonthTradingTotal1', `${biTradingModelState.trading1TodayAmountTotal} - ${trading1MonthBeforeTodayAmountTotal} = ${thisMonthTradingTotal1}`);
    // console.log('thisMonthTradingTotal2', `${biTradingModelState.trading2AmountTotal} - ${trading2BeforeMonthAmountTotal} = ${thisMonthTradingTotal2}`);
    // console.log('tradingTotal', `${beforeMonthTradingTotal} + ${thisMonthTradingTotal1} + ${thisMonthTradingTotal2} = ${beforeMonthTradingTotal + thisMonthTradingTotal1 + thisMonthTradingTotal2}`);
    return Math.round(beforeMonthTradingTotal + thisMonthTradingTotal1 + thisMonthTradingTotal2);
  }, [beforeMonthTradingTotal, thisMonthTradingTotal1, thisMonthTradingTotal2]);

  const { chart, echarts, setContainerRef } = useEcharts({
    theme: 'default',
    options: {
      renderer: 'canvas'
    }
  });

  // const { chart: chart2, setContainerRef: setContainerRef2 } = useEcharts();

  const { data } = useRequest(() =>
    fetch('/jsons/dounan.json').then(res => res.json())
  );


  useEffect(() => {
    if (!chart || !data) return;
    echarts.registerMap('dounan', data);
    const regions = data.features.map(function (feature: any) {
      const result = {
        name: feature.properties.name,
        value: Math.random(),
        height: feature.properties.height / 2,
        itemStyle: {
          color: '#1687C5',
          opacity: 1,
        }
      };
      if (['主场馆', '草花交易区', '百合区', '拍卖提货区', '车花过渡市场'].includes(feature.properties.name.split('-')[0])) {
        result.itemStyle.color = '#02BFB1';
      }
      return result
    });


    const option = {
      geo3D: {
        map: 'dounan',
        roam: true,
        shading: 'lambert',
        itemStyle: {
          color: '#142957',
          opacity: 1,
          borderWidth: 0.4,
          borderColor: '#0692a4'
        },
        label: {
          show: true,
          formatter: (params: any) => {
            if (params.name === '草花交易区') {
              return '草花\n交易区'
            }
            if (params.name === '拍卖提货区') {
              return '拍卖\n提货区'
            }
            return params.name.split('-')[0]
          },
          color: '#000',
          fontSize: 12,
          lineHeight: 16,
          backgroundColor: '#FDBF44',
          padding: [4, 4],
          borderRadius: 5,
        },
        selectedMode: false,
        silent: true,
        emphasis: {
          label: {
            show: false
          }
        },
        light: {
          main: {
            intensity: 0.6,
            alpha: 40,
            shadow: true,
            shadowQuality: 'high'
          },
          ambient: {
            intensity: 0.6
          }
        },
        groundPlane: {
          show: false,
        },
        right: 100,
        top: 20,
        viewControl: {
          projection: 'orthographic',
          orthographicSize: 70,
          beta: 30,
          alpha: 40,
          center: [0, 5, 0],
          panMouseButton: 'left',
          rotateMouseButton: 'middle',
          zoomSensitivity: 0.5
        },
        regions,
      },
      series: [
      ]
    };

    chart.setOption(option);
  }, [chart, data]);



  return (
    <div className='relative w-full h-full pl-70px'>
      <div className='absolute top-40px left-610px z-10 text-cyan-100 w-[200px] flex flex-col gap-2 '>
        <div className='text-18px text-yellow-100'>年交易额(元)</div>
        <div className='text-2xl font-bold flex items-center gap-2'><BiStatisticsNum3 value={tradingTotal || 0} size={20} showSplit /></div>
      </div>
      <div ref={setContainerRef} className='relative w-full h-full' />
    </div>
  );
};

export default Bi01Building; 
