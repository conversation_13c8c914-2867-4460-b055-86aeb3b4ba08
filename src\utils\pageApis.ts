import { apiAdminUserPageList, apiMerchantUserEnabledChange, apiArticleCreateOrUpdate, apiArticleGetById, apiArticleDeleteById, apiArticlePageList, apiArtworkCreateOrUpdate, apiArtworkGetById, apiArtworkDeleteById, apiArtworkPageList, apiAuctionBidRelatList, apiAuctionGet, apiBillRelatList, apiBillRefundById, apiAuctionCreateOrUpdate, apiAuctionDeleteById, apiAuctionPageList, apiBannerCreateOrUpdate, apiBannerDeleteById, apiBannerPageList, apiBillPageList, apiBrokerCreateOrUpdate, apiBrokerDeleteById, apiBrokerPageList, apiCouponBlankCreate, apiCouponBlankPageList, apiCouponBlankUpdate, apiCouponCreateByTpl, apiCouponPageList, apiCouponTplGetById, apiUploadFile, apiClientUserPageList, apiCouponTplCreateOrUpdate, apiCouponTplDeleteById, apiCouponTplPageList, apiDictAllTags, apiDictCreateOrUpdate, apiDictDeleteById, apiDictPageList, apiFeedbackPageList, apiFeedbackUpdate, apiLcOrderGetById, apiLcOrderPageList, apiMediaDeleteById, apiMediaPageList, apiMerchantUserCreateByMobiles, apiMerchantUserDeleteById, apiMerchantUserRealInfoGet, apiMerchantUserUpdate, apiAuctionRelatList, apiShopGetById, apiShopCreateOrUpdate, apiShopPageList, apiShopRank, apiShopSyncConflictHandle, apiShopSyncConflictPageList, apiTricycleDepositPageList, apiTricycleGetById, apiTricycleIllegalPageList, apiTricycleIllegalFinePayFromDeposit, apiTricycleIllegalRuleCreateOrUpdate, apiTricycleIllegalRuleDeleteById, apiTricycleIllegalRulePageList, apiTricyclePageList, apiTricycleQrCode, apiUserRealInfoGet, apiWorkflowAction, apiWorkflowGetActions, apiWorkflowGetById, apiWorkflowPageList } from "@/apis/apis.api";

export default {
    "adminUserList": [
        apiAdminUserPageList.url,
        apiMerchantUserEnabledChange.url
    ],
    "articleEdit": [
        apiArticleCreateOrUpdate.url,
        apiArticleGetById.url
    ],
    "articleList": [
        apiArticleCreateOrUpdate.url,
        apiArticleDeleteById.url,
        apiArticlePageList.url
    ],
    "artworkEdit": [
        apiArtworkCreateOrUpdate.url,
        apiArtworkGetById.url
    ],
    "artworkList": [
        apiArtworkCreateOrUpdate.url,
        apiArtworkDeleteById.url,
        apiArtworkPageList.url
    ],
    "auctionDetail": [
        apiAuctionBidRelatList.url,
        apiAuctionGet.url,
        apiBillRelatList.url,
        apiBillRefundById.url
    ],
    "auctionEdit": [
        apiAuctionCreateOrUpdate.url,
        apiAuctionGet.url
    ],
    "auctionList": [
        apiAuctionDeleteById.url,
        apiAuctionPageList.url
    ],
    "bannerList": [
        apiBannerCreateOrUpdate.url,
        apiBannerDeleteById.url,
        apiBannerPageList.url
    ],
    "billList": [
        apiBillPageList.url,
        apiBillRefundById.url
    ],
    "brokerList": [
        apiBrokerCreateOrUpdate.url,
        apiBrokerDeleteById.url,
        apiBrokerPageList.url
    ],
    "couponList": [
        apiCouponBlankCreate.url,
        apiCouponBlankPageList.url,
        apiCouponBlankUpdate.url,
        apiCouponCreateByTpl.url,
        apiCouponPageList.url,
        apiCouponTplGetById.url,
        apiUploadFile.url,
        apiClientUserPageList.url
    ],
    "couponTplList": [
        apiCouponTplCreateOrUpdate.url,
        apiCouponTplDeleteById.url,
        apiCouponTplPageList.url,
        apiAdminUserPageList.url
    ],
    "dictList": [
        apiDictAllTags.url,
        apiDictCreateOrUpdate.url,
        apiDictDeleteById.url,
        apiDictPageList.url
    ],
    "feedbackList": [
        apiFeedbackPageList.url,
        apiFeedbackUpdate.url
    ],
    "lcOrderDetail": [
        apiLcOrderGetById.url
    ],
    "lcOrderList": [
        apiLcOrderPageList.url
    ],
    "mediaList": [
        apiMediaDeleteById.url,
        apiMediaPageList.url
    ],
    "merchantUserList": [
        apiMerchantUserCreateByMobiles.url,
        apiMerchantUserDeleteById.url,
        apiMerchantUserEnabledChange.url,
        apiAdminUserPageList.url,
        apiMerchantUserRealInfoGet.url,
        apiMerchantUserUpdate.url
    ],
    "shopDetail": [
        apiAuctionRelatList.url,
        apiShopGetById.url
    ],
    "shopEdit": [
        apiShopCreateOrUpdate.url,
        apiShopGetById.url
    ],
    "shopList": [
        apiShopCreateOrUpdate.url,
        apiShopPageList.url,
        apiShopRank.url
    ],
    "shopSyncConflictList": [
        apiShopSyncConflictHandle.url,
        apiShopSyncConflictPageList.url
    ],
    "tricycleDetail": [
        apiTricycleDepositPageList.url,
        apiTricycleGetById.url,
        apiTricycleIllegalPageList.url
    ],
    "tricycleIllegalList": [
        apiTricycleIllegalFinePayFromDeposit.url,
        apiTricycleIllegalPageList.url
    ],
    "tricycleIllegalRuleList": [
        apiTricycleIllegalRuleCreateOrUpdate.url,
        apiTricycleIllegalRuleDeleteById.url,
        apiTricycleIllegalRulePageList.url
    ],
    "tricycleList": [
        apiTricyclePageList.url,
        apiTricycleQrCode.url
    ],
    "userList": [
        apiClientUserPageList.url,
        apiUserRealInfoGet.url
    ],
    "workflowDetail": [
        apiWorkflowAction.url,
        apiWorkflowGetActions.url,
        apiWorkflowGetById.url
    ],
    "workflowList": [
        apiWorkflowPageList.url
    ]
}