import { apiAdminPermissionPageList } from "@/apis/apis.api";
import { useMemoizedFn, useRequest } from "ahooks";
import { Modal, Transfer } from "antd";
import { useEffect, useState } from "react";

export interface TAdminPermissionModalProps {
  open: boolean;
  value: string[];
  onChange: (value: string[]) => void;
  onClose: () => void;
}

function AdminPermissionModal(props: TAdminPermissionModalProps) {

  const [targetKeys, setTargetKeys] = useState<string[]>(props.value)

  useEffect(() => {
    setTargetKeys(props.value)
  }, [props.value])

  const { data: permissionList } = useRequest(async () => {
    const res = await apiAdminPermissionPageList({
      pageNum: 1,
      pageSize: 10000,
      filter: [],
      sort: [],
    });
    return res.list.map(item => ({
      key: item.code,
      title: item.name,
      description: item.code
    }))
  })

  const onChange = (nextTargetKeys: string[]) => {
    setTargetKeys(nextTargetKeys);
  }

  const onSubmit = () => {
    props.onChange(targetKeys);
    props.onClose();
  }

  const filterOption = useMemoizedFn((inputValue: string, item: any) => {
    return (item.key || '').indexOf(inputValue) !== -1 || (item.title || '').indexOf(inputValue) !== -1 || (item.description || '').indexOf(inputValue) !== -1;
  })

  return (
    <Modal
      title="设置权限"
      open={props.open}
      onCancel={props.onClose}
      onOk={onSubmit}
      // footer={null}
      width={800}
      destroyOnClose
    >
      <Transfer
        listStyle={{
          width: 360,
          height: 500,
        }}
        dataSource={permissionList || []}
        targetKeys={targetKeys}
        showSearch
        // showSelectAll={false}
        onChange={onChange}
        filterOption={filterOption}
        render={(item) => <div>
          <div>{item.title}</div>
          <div style={{fontSize: 12, color: '#999'}}>{item.description}</div>
          
        </div>}
      />
    </Modal>
  )
}

export default AdminPermissionModal
