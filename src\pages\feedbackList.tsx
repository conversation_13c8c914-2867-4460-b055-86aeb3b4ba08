import { apiFeedbackPageList, apiFeedbackProcess } from "@/apis/apis";
import { useFormModal } from "@/components/FormilyModal";
import PreviewFileList from "@/components/PreviewFileList";
import UserObj from "@/components/UserObj";
import { AgreeDicts } from "@/utils/agreeDict";
import { ToolsUtil } from "@/utils/tools";
import { ActionType, ProTable } from "@ant-design/pro-components";
import { useMemoizedFn } from "ahooks";
import { App, Space } from "antd";
import { useRef } from "react";

type TFormData = {
  remark: string;
  reply: string;
  status: string;
}

function FeedbackList() {
  const { formModalRef, formModalHolder } = useFormModal<TFormData>();
  const { message } = App.useApp();
  const tableRef = useRef<ActionType>();


  const showEditModal = useMemoizedFn(async (record: TFeedback) => {
    formModalRef.current?.show({
      modalTitle: '处理',
      initialValues: ({
        remark: record.remark,
        reply: record.reply,
        status: AgreeDicts.Feedback_status.labelValueMap['已处理'],
      }),
      modalWidth: 1000,
      onAutoSubmit: async (values) => {
        await apiFeedbackProcess({
          ...values,
          id: record.id
        })
        message.success("提交成功");
        tableRef.current?.reload();
      },
      schema: {
        type: "object",
        properties: {
          status: {
            title: "处理状态",
            type: "string",
            "x-decorator": "FormItem",
            "x-component": "Select",
            "x-component-props": {
              options: AgreeDicts.Feedback_status.options,
            },
          },
          remark: {
            title: "备注(用户不可见)",
            type: "string",
            "x-decorator": "FormItem",
            "x-component": "Input.TextArea",
          },
          reply: {
            title: "回复",
            type: "string",
            "x-decorator": "FormItem",
            "x-component": "Input.TextArea",
          },

        },
      },
    })
  })

  return (
    <div>
      <ProTable<TFeedback>
        headerTitle="投诉建议列表"
        actionRef={tableRef}
        rowKey="_id"
        search={{
          defaultCollapsed: false,
        }}
        toolBarRender={() => [
          
        ]}
        pagination={{
          pageSize: 20,
          showQuickJumper: true, // 显示快速跳转
        }}
        columns={[
          { dataIndex: 'content', title: '内容', search: false },
          {
            dataIndex: 'attachments',
            title: '图片',
            search: false,
            width: 100,
            render: (_text, record) => {
              return (
                <PreviewFileList urls={record.attachments} type="button" >
                </PreviewFileList>
              )
            }
          },
          { dataIndex: 'clientUser', title: '用户', width: 120, render: (_text, record) => <UserObj userObj={record.clientUser} /> },
          { dataIndex: 'mobile', title: '电话', width: 120, },
          {
            dataIndex: 'createdAt',
            title: '创建时间',
            valueType: 'dateRange',
            search: false,
            render: (_text, record) => ToolsUtil.timeFormat(record.createdAt),
            width: 180,
          },
          {
            dataIndex: 'status',
            title: '处理状态',
            valueType: 'select',
            valueEnum: AgreeDicts.Feedback_status.valueLabelMap,
          },
          { dataIndex: 'remark', title: '备注', search: false },
          { dataIndex: 'reply', title: '回复', search: false },
          {
            dataIndex: 'action', title: '操作', search: false,  width: 80, render: (_text, record) => (
              <Space>
                {record.status === 'PENDING' ? <a onClick={() => showEditModal(record)}>处理</a> : null}
              </Space>
            )
          },
        ]}

        request={async (params) => {
          const res = await apiFeedbackPageList({
            pageIndex: params.current - 1,
            pageSize: params.pageSize,
            status: params.status,
            mobile: params.mobile,
          })
          return {
            data: res.rows,
            total: res.totalPageCount,
            success: true,
          }
        }}
      >
      </ProTable >
      {formModalHolder}
    </div>
  );
}

export default FeedbackList;

