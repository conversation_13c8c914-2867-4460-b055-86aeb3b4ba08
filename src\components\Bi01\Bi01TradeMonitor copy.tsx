import { useModel } from "foca";
// import BiVideoMonitor from "../BiVideoMonitor";
import { settingModel } from "@/stores/settingModel";
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay } from 'swiper/modules';
import 'swiper/css';
import LiveVideo from "../LiveVideo";
import { useCreation } from "ahooks";
const Bi01TradeMonitor = () => {
    const videos = useModel(settingModel, (state) => state.Admin?.buildingVideos || []);
    const titles = useCreation(() => {
        return videos.map((video) => {
            const url = new URL(video);
            return url.searchParams.get('title');
        });
    }, [videos]);



    return (
        <div className="w-full h-full flex justify-between">
            <Swiper
                modules={[Autoplay]}
                spaceBetween={50}
                slidesPerView={3}
                autoplay={{
                    delay: 10 * 1000,
                    disableOnInteraction: false
                }}
                loop
                observer
                observeParents
            //   onSlideChange={(swiper) => {
            //     const index = swiper.activeIndex;

            //   }}
            // //   onSwiper={(swiper) => console.log(swiper)}
            >
                {videos.map((video, index) => (
                    <SwiperSlide key={index}>
                        <div className="text-cyan-100 text-center mb-2">{titles[index]}</div>
                        <LiveVideo
                            //   ref={el => (videoRefs.current[index] = el)}
                            src={video}
                            className="w-[416px] h-full object-cover rounded-xl bg-black"
                            width={416}
                            height={234}
                            autoPlay
                            muted
                            onError={(error) => console.error('Video error:', error)}
                        />
                    </SwiperSlide>
                ))}
            </Swiper>

        </div>
    );
};

export default Bi01TradeMonitor;
