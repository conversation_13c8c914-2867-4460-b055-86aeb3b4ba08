import { useEffect } from 'react';
import { useEcharts } from '@/hooks/useEcharts';
import { ToolsUtil } from '@/utils/tools';
import { useCreation, useRequest } from 'ahooks';

const Bi01Trading24h = () => {
  const { chart, setContainerRef } = useEcharts();

  const { data: tridingData } = useRequest(async () => {
    const response = await fetch('/jsons/trading.json');
    return response.json();
  })

  const data = useCreation(() => {
    if (!tridingData) return null;
    const areas = ['玫瑰区', '康乃馨区', '百合区', '绣球区', '杂花区']

    const result: {
      name: string;
      list: {
        date: string;
        total: number;
      }[]
    }[] = [];

    ToolsUtil.random.range(tridingData.length).forEach((i) => {

      areas.forEach((area, ai) => {
        if (!result[ai]) {
          result[ai] = {
            name: area,
            list: []
          }
        }
        result[ai].list.push({
          date: (i + 1) + '月',
          total: tridingData[i][area],
        });
      });
    })

    return result;
  }, [tridingData])

  useEffect(() => {

    if (!chart || !data) return;
    let index = 0;
    const run = () => {
      chart.dispatchAction({
        type: 'showTip',
        seriesIndex: 0,
        dataIndex: index
      })
      index++
      if (index > tridingData.length) {
        index = 0;
      }
    }
    run();
    const timer = setInterval(run, 10 * 1000)

    return () => {
      clearInterval(timer);
    }


  }, [tridingData, chart])

  useEffect(() => {
    if (!data || !chart) return;
    const colors = [
      '#FF4D4F',  // 鲜红
      '#13C2C2',  // 青色
      '#FAAD14',  // 金黄
      '#722ED1',  // 紫色
      '#52C41A',  // 绿色
      '#1890FF',  // 蓝色
      '#EB2F96',  // 粉红
      '#FA8C16',  // 橙色
      '#2F54EB',  // 深蓝
      '#F5222D',  // 红色
    ];
    const areas = data.map(item => item.name);
    const hours = data[0].list.map(item => item.date);

    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.6)',
        borderColor: 'rgba(255,255,255,0.2)',
        textStyle: {
          color: '#fff'
        },
        formatter: (params: any[]) => {
          let result = params[0].axisValue + '<br/>';
          params.forEach(item => {
            result += `${item.marker} ${item.seriesName}: ${item.value}万元<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: areas,
        textStyle: {
          color: '#E6EAF2'
        },
      },
      grid: {
        left: 0,
        right: 50,
        bottom: 0,
        top: 40,
        containLabel: true
      },
      xAxis: {
        name: '月份',
        nameTextStyle: {
          color: '#02CABB',
          align: 'left',
        },
        type: 'category',
        boundaryGap: false,
        data: hours,
        axisLine: {
          lineStyle: {
            color: '#37435A'
          }
        },
        axisLabel: {
          color: '#E6EAF2'
        }
      },
      yAxis: {
        name: '金额(万元)',
        nameTextStyle: {
          color: '#02CABB',
        },
        type: 'value',
        splitLine: {
          lineStyle: {
            color: '#37435A'
          }
        },
        axisLabel: {
          color: '#E6EAF2',
          formatter: (value: number) => {
            return value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value;
          }
        }
      },
      series: areas.map((area, ai) => ({
        name: area,
        type: 'line',
        smooth: true,
        symbol: 'none',
        itemStyle: {
          color: colors[ai % colors.length]
        },
        data: data[ai].list.map(item => item.total),
        lineStyle: {
          width: 2,
          color: colors[ai % colors.length],
        },
      }))
    };
    chart.clear();
    chart.setOption(option);
  }, [chart, data]);

  return <div ref={setContainerRef} className="w-full h-full" />;
};

export default Bi01Trading24h;
