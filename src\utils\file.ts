import { message, UploadFile } from 'antd';

export default class FileUtil {
  /**
   * 上传文件列表转换为ids
   * @param items
   */
  static uploadFilesToUrls(items?: UploadFile[]): string[] {
    return (items || []).map((item) => item.response?.data?.url || '').filter((item) => !!item);
  }

  /**
   * 服务端的文件列表转换为上传文件列表
   * @param item
   */
  static urlsToUploadFiles(items?: string[]): UploadFile[] {
    return (items || []).filter(item => !!item).map((sourceUrl) => {
      const url = sourceUrl.split('?')[0];
      const ext = url.split('.').pop();
      const name = url.split('/').pop();
      const type =
        {
          jpg: 'image/jpeg',
          png: 'image/png',
          gif: 'image/gif',
          mp4: 'video/mp4',
          avi: 'video/x-msvideo',
          mov: 'video/quicktime',
          mp3: 'audio/mpeg',
          wav: 'audio/wav',
          pdf: 'application/pdf',
          doc: 'application/msword',
          docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          xls: 'application/vnd.ms-excel',
          xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          ppt: 'application/vnd.ms-powerpoint',
          pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          zip: 'application/zip',
          rar: 'application/x-rar-compressed',
          txt: 'text/plain',
        }[ext] || 'application/octet-stream';
      return {
        uid: name,
        name,
        type,
        status: 'done',
        response: {
          data: {
            url,
          },
        },
        url: type.startsWith('image') ? `${url}?x-oss-process=image/resize,w_750` : url,
      };
    });
  }

  static urlToPreviewFile(url: string, options?: { width?: number; height?: number }): TPreviewFile {
    url = url || ''
    const { width = 100, height = 100 } = options || {};
    const ext = url.split('.').pop().toLowerCase();
    const name =decodeURIComponent( url.split('/').pop());
    const type =
      {
        jpg: 'image',
        jpeg: 'image',
        png: 'image',
        gif: 'image',
        webp: 'image',
        mp4: 'video',
        mov: 'video',
        avi: 'video',
        webm: 'video',
        ogg: 'video',
        pdf: 'pdf',
      }[ext] || 'unknown';
    const previewUrl =
      {
        image: `${url}?x-oss-process=image/resize,m_fill,w_${width},h_${height}`,
        video: `${url}?x-oss-process=video/snapshot,t_1000,f_jpg,w_${width}`,
      }[type] || url;
    return { previewUrl, type: type, url, name } as TPreviewFile;
  }

  /** 资源地址转换成可以浏览的 */
  static urlsToPreviewFiles(urls?: string[], options?: { width?: number; height?: number }): TPreviewFile[] {
    return (urls || []).filter(item => item.startsWith('http')).map((url) => this.urlToPreviewFile(url, options));
  }

  static beforeUpload = async (file: any) => {
    return new Promise<boolean>((resolve, reject) => {
      const isImage = (file.type || '').startsWith('image/');
      if (isImage) {
        const isGt20M = file.size / 1024 / 1024 > 20;
        if (isGt20M) {
          message.error('图片不能超过20MB');
          return reject(false)
        }
        const image = new window.Image();
        image.src = URL.createObjectURL(file);
        image.onload = () => {
          const width = image.width;
          const height = image.height;
          if (width > 3000 || height > 3000) {
            message.error('图片不能超过3000*3000');
            return reject(false)
          }
        }
      }
      return resolve(true)
    })
  };
}
