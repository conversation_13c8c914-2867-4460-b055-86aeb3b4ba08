
import {
  connect,
  mapReadPretty,
} from '@formily/react'
import { useEffect, useRef } from "react";

const extToType = (url: string) => {
  const ext = (url.split('.').pop()).toLowerCase()
  return {
    jpg: 'image',
    jpeg: 'image',
    png: 'image',
    gif: 'image',
    webp: 'image',
    '3gp': 'video',
    mpg: 'video',
    mpeg: 'video',
    mp4: 'video',
    m4v: 'video',
    m4p: 'video',
    ogv: 'video',
    ogg: 'video',
    mov: 'video',
    webm: 'video',
  }[ext]
}


const MediaSize = (props: any) => {
  const lastTime = useRef(0)
  
  useEffect(() => {
    const time = Date.now()
    lastTime.current = time
    const type = extToType(props.url || '')
    if (type === 'image') {
      const img = new Image()
      img.src = props.url
      img.onload = () => {
        if (lastTime.current === time) {
          props.onChange({
            width: img.width,
            height: img.height,
          })
        }
        img.remove()
      }
    } else if (type === 'video') {
      const video = document.createElement('video')
      video.src = props.url
      video.onloadedmetadata = () => {
        if (lastTime.current === time) {
          props.onChange({
            width: video.videoWidth,
            height: video.videoHeight,
          })
        }
        video.remove()
      }
    }
  }, [props.url])

  return <div>{props.value ? `${props.value?.width} x ${props.value?.height}` : null}</div>;
};

export default connect(
  MediaSize,
  mapReadPretty(() => null),
);
