import pageApis from "./pageApis";

const pageNeedFunctions: Record<string, string[]> = pageApis;

// const authPagesCache = new Map<string, boolean>();

/**
 * 判断是否可以跳转到某个页面
 */
export function canGoToPageByFunctions(
  pagePath: string,
  accountFunctions: string[]
) {
  if (!pageNeedFunctions[pagePath]) {
    return true;
  }
  // if (authPagesCache.has(pagePath)) {
  //   return authPagesCache.get(pagePath) || false;
  // }

  const needCodes = (pageNeedFunctions[pagePath] || []).filter(
    (code) => !!code
  );
  const res = needCodes.every((code) => accountFunctions.includes(code));
  if (accountFunctions.length > 0 && !res) {
    console.log(pagePath, needCodes.filter((code) => !accountFunctions.includes(code)));
  }

  // authPagesCache.set(pagePath, res);
  return res;
}
