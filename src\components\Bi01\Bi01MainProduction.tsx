// import { useEffect } from 'react';
import { useRequest } from 'ahooks';

// import { useEcharts } from '@/hooks/useEcharts';
import { apiBiTruckCount } from '@/apis/apis.api';
import BiAutoScrollView from '../BiAutoScrollView';


const imgList = [
  "/images/bi/rank1.png",
  "/images/bi/rank2.png",
  "/images/bi/rank3.png",
  "/images/bi/rankOther.png",
]


const Bi01MainProduction = () => {

  const { data } = useRequest(async () => {
    const res = await apiBiTruckCount();
    const max = res.reduce((pre, cur) => Number(pre) > Number(cur.count) ? pre : Number(cur.count), 0)
    return res.sort((a, b) => b.count - a.count).map((item, index) => {
      return {
        name: item.StopName,
        value: item.count,
        percent: (item.count / max * 100).toFixed(2),
        style: {
          fontSize: index < 3 ? 6 : 8,
          backgroundColor: {
            image: `url(${imgList[Math.min(index, 4)]})`,
          }
        }
      }
    });
  });

  return (
    <div>
      <BiAutoScrollView itemHeight={66} height={330} delay={3000}>
        {(data || []).map((item, index) => (
          <div key={index}
            className='relative h-56px w-full overflow-hidden mb-10px text-cyan-100'
          >
            <div className='flex items-center gap-2'>
              <div className='w-20px h-20px shrink-0 flex items-center justify-center' style={{
                fontSize: index < 3 ? 0 : index < 9 ? 12 : 8,
                backgroundImage: `url(${imgList[Math.min(index, 3)]})`,
                backgroundSize: '100% 100%',
                fontWeight: 'bold',
              }}>{index + 1}</div>
              <div>{item.name}</div>
            </div>
            <div className='flex items-center gap-2'>
              <div className='flex-1 h-10px bg-#0095FF20 relative'>
                <div className='absolute left-0 top-0 h-full' style={{ width: `${item.percent}%`, background: 'linear-gradient(to right, #0095FF, #91E4FF)' }}></div>
              </div>
              <div className='w-40px shrink-0 text-blue-300'>{item.value}辆</div>
            </div>
          </div>
        ))}
      </BiAutoScrollView>
    </div>
  )
};

export default Bi01MainProduction;
