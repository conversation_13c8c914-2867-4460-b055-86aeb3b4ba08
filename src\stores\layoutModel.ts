import { defineModel } from 'foca';


export const layoutModel = defineModel('layout', {
  initialState: {
    headerHeight: 40,
    sidebarWidth: 200,
    navbarHeight: 40,
    windowWidth: window.innerWidth,
    windowHeight: window.innerHeight,
  },
  // computed: {
  //   mainHeight() {
  //     return this.windowHeight - this.headerHeight - this.navbarHeight;
  //   },
  //   navbarWidth() {
  //     return this.windowWidth - this.sidebarWidth;
  //   },
  //   mainWidth() {
  //     return this.windowWidth - this.sidebarWidth;
  //   },
  // },

  reducers: {
    onWindowResize(state) {
      state.windowHeight = window.innerHeight;
      state.windowWidth = window.innerWidth;
    },

  },
  methods: {
   
  },
  events: {
    onInit() {
      console.log('layoutModel init');
      window.addEventListener('resize', () => {
        console.log('layoutModel resize');
        this.onWindowResize();
      });
    },
  },
});
