import { apiBillClose, apiBill<PERSON>ageList, apiBillTriggerAutoCloseTask } from "@/apis/apis";
import UserObj from "@/components/UserObj";
import { useBillRemarkFormModal } from "@/hooks/useBillRemarkFormModal";
import { AgreeDicts } from "@/utils/agreeDict";
import { ToolsUtil } from "@/utils/tools";
import { ActionType, ProTable } from "@ant-design/pro-components";
import { App, Button, Popconfirm, Space, Tooltip } from "antd";
import { useRef } from "react";
import { useNavigate } from "react-router-dom";

function BillList() {
  const tableRef = useRef<ActionType>();
  const navigate = useNavigate();
  const { message } = App.useApp();

  const { showEditModal, formModalHolder } = useBillRemarkFormModal(() => {
    tableRef.current?.reload();
  });

  const handleTriggerAutoClose = async () => {
    try {
      await apiBillTriggerAutoCloseTask();
      tableRef.current?.reload();
      message.success('触发成功');
    } catch (error) {
      console.error('触发失败', error);
      message.error('触发失败');
    }
  };


  const handleCloseBill = async (data: TBill) => {
    if (!data) return;

    try {
      await apiBillClose(data.id, { reason: '手动关闭' });
      message.success('关闭成功');
      // 重新获取数据以更新界面
      tableRef.current?.reload();
    } catch (error) {
      console.error('关闭失败', error);
      message.error('关闭失败');
    }
  };

  return (
    <div>
      <ProTable<TBill>
        headerTitle="账单列表"
        actionRef={tableRef}
        rowKey="id"
        toolbar={{
          actions: [
            <Popconfirm key="triggerAutoClose" title="确认关闭过期账单" description="确定要关闭所有过期账单吗？" onConfirm={handleTriggerAutoClose}>
              <Button key="triggerAutoClose" type="primary">
                关闭过期账单
              </Button>
            </Popconfirm>,
          ],
        }}
        search={{
          defaultCollapsed: false,
        }}
        pagination={{
          pageSize: 20,
          showQuickJumper: true,
        }}
        columns={[
          { dataIndex: 'id', title: 'ID', search: true, width: 180 },
          { dataIndex: 'orderType', title: '订单类型', valueType: 'select', valueEnum: AgreeDicts.Bill_orderType.valueLabelMap },
          { dataIndex: 'orderId', title: '订单ID' },
          { dataIndex: 'clientUserKeyword', title: '客户', render: (_text, record) => <UserObj userObj={record.clientUser} /> },

          { dataIndex: 'originalMoneyCent', title: '原始金额', search: false, render: (text: number) => ToolsUtil.moneyCentFormat(text) },
          { dataIndex: 'couponDiscountCent', title: '优惠券折扣', search: false, render: (text: number) => ToolsUtil.moneyCentFormat(text) },
          { dataIndex: 'pointDiscountCent', title: '积分折扣', search: false, render: (text: number) => ToolsUtil.moneyCentFormat(text) },
          { dataIndex: 'orderMoneyCent', title: '订单金额', search: false, render: (text: number) => ToolsUtil.moneyCentFormat(text) },
          { dataIndex: 'paidMoneyCent', title: '支付金额', search: false, render: (text: number) => ToolsUtil.moneyCentFormat(text) },
          { dataIndex: 'refundedMoneyCent', title: '退款金额', search: false, render: (text: number) => ToolsUtil.moneyCentFormat(text) },
          { dataIndex: 'wechatPayNo', title: '微信支付单号', search: true },
          {
            dataIndex: 'payStatus',
            title: '支付状态',
            search: true,
            valueType: 'select',
            valueEnum: AgreeDicts.Bill_payStatus.valueLabelMap,
          },
          {
            dataIndex: 'paidAt',
            title: '支付时间',
            valueType: 'dateRange',
            search: true,
            render: (_, record) => record.paidAt ? ToolsUtil.timeFormat(record.paidAt) : '-',
          },
          {
            dataIndex: 'remark', title: '备注', search: true, render: (text: string) => {
              return (text || '').length > 5 ? <Tooltip title={text}>{text.slice(0, 5)}</Tooltip> : text;
            }
          },
          {
            dataIndex: 'createdAt',
            title: '创建时间',
            valueType: 'dateRange',
            search: true,
            render: (_, record) => ToolsUtil.timeFormat(record.createdAt),
          },
          {
            dataIndex: 'action',
            title: '操作',
            search: false,
            width: 120,
            render: (_, record) => (
              <Space>
                <a onClick={() => navigate(`/billDetail?id=${record.id}`)}>查看</a>
                <a onClick={() => showEditModal(record)}>编辑备注</a>
                {record.payStatus === AgreeDicts.Bill_payStatus.labelValueMap['未支付'] && (
                  // <a onClick={() => handleCloseBill(record)}>关闭</a>
                  <Popconfirm title="确认关闭账单" description="确定要关闭此账单并退回积分吗？" onConfirm={() => handleCloseBill(record)}>
                    <a>关闭</a>
                  </Popconfirm>
                )}
              </Space>
            )
          },
        ]}
        request={async (params) => {
          // 处理日期范围
          const [paidAtStart, paidAtEnd] = params.paidAt || [];
          const [createdAtStart, createdAtEnd] = params.createdAt || [];

          const res = await apiBillPageList({
            pageIndex: params.current - 1,
            pageSize: params.pageSize,
            id: params.id,
            orderType: params.orderType,
            orderId: params.orderId,
            clientUserId: params.clientUserId,
            clientUserKeyword: params.clientUserKeyword,
            payStatus: params.payStatus,
            wechatPayNo: params.wechatPayNo,
            paidAtStart: paidAtStart,
            paidAtEnd: paidAtEnd,
            createdAtStart: createdAtStart,
            createdAtEnd: createdAtEnd,
          });
          return {
            data: res.rows,
            total: res.totalRowCount,
            success: true,
          };
        }}
      />
      {formModalHolder}
    </div>
  );
}

export default BillList;
