import { useEffect } from "react";
import { authModel } from '@/stores/authModel';
import { useModel } from "foca";


function HomePage() {
  const authModelState = useModel(authModel);
  useEffect(() => {
    if(authModelState.logined) {
      if(!authModelState.account) {
        // 跳转到登录页面
        window.location.href = '/login'
      } else {
        // 跳转到首页
        window.location.href = '/dashboard'
      }
    }
   
  }, [authModelState.logined])
  return null
}

export default HomePage;