import { ActionType, ProTable } from "@ant-design/pro-components";
import { Modal } from "antd";
import { forwardRef, useImperativeHandle, useRef, useState } from "react";
import { apiTricycleInfoRecordPageList } from "@/apis/apis.api";
import PreviewFileList from "../PreviewFileList";
import UserObj from "../UserObj/UserObj";
import { ToolsUtil } from "@/utils/tools";

export type TTricycleApplyRecordListRef = {
    show: (tricycleId: string) => void
}

const TricycleApplyRecordList = forwardRef<TTricycleApplyRecordListRef, any>((_props, ref) => {

    const [open, setOpen] = useState(false)
    const tableRef = useRef<ActionType>();
    const tricycleId = useRef<string>('')

    useImperativeHandle(ref, () => ({
        show: (id) => {
            tricycleId.current = id
            tableRef.current?.reload()
            setOpen(true)
        },
    }))

    return <Modal open={open} onCancel={() => setOpen(false)} width={1200}>
        <ProTable<TTricycleInfoRecord>
            actionRef={tableRef}
            headerTitle="三轮车填写记录"
            rowKey="id"
            search={false}
            pagination={false}
            columns={[
                {
                    title: '商家用户',
                    dataIndex: 'userObj',
                    render: (_text, record) => <UserObj userObj={record.userObj} />,
                },
                {
                    title: '图片',
                    dataIndex: 'images',
                    render: (_text, record) => <PreviewFileList urls={record.images} type="button" />,
                },
                {
                    title: '填写时间', dataIndex: 'createdAt',
                    valueType: 'dateRange',
                    render: (_text, record) => ToolsUtil.timeFormat(record.createdAt)
                },
            ]}
            request={async (params) => {
                const res = await apiTricycleInfoRecordPageList({
                    pageNum: 1,
                    pageSize: 100,
                    filter: [
                        { type: 'eq', field: 'tricycleId', value: tricycleId.current },
                    ],
                    sort: [],
                })
                return {
                    data: res.list,
                    success: true,
                }
            }}
        />
    </Modal>;
});

export default TricycleApplyRecordList;