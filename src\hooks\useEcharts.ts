import { useEffect, useState } from 'react';
import * as echarts from 'echarts';
import { useMemoizedFn } from 'ahooks';
import walden from './echarts-theme-walden'
import 'echarts-gl';

echarts.registerTheme('walden', walden);

export function useEcharts(params?: {
  // container: HTMLDivElement;
  theme?: string;
  options?: {
    renderer?: 'svg' | 'canvas';
    devicePixelRatio?: number;
    width?: number;
    height?: number;
  };
}) {
  const { theme = 'walden', options } = params || {};
  const [container, setContainer] = useState<HTMLDivElement>(null);
  
  const [chart, setChart] = useState<echarts.ECharts | null>(null);

  const setContainerRef = useMemoizedFn((dom: HTMLDivElement) => {
    setContainer(dom)
  })

  useEffect(() => {
    if (!container) return;
    const newChart = echarts.init(container, theme, {
      renderer: 'svg',
      width: container.clientWidth || 400,
      height: container.clientHeight || 300,
      ...options
    });
    setChart(newChart);

    return () => {
      newChart.dispose();
    }
  }, [container]);


  return {
    echarts,
    chart,
    container,
    setContainerRef,
  };
}
