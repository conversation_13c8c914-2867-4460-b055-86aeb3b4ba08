import { apiBiPeopleCountInAndOutReport } from "@/apis/apis.api";
import { useEcharts } from "@/hooks/useEcharts";
import { useStepedRequest } from "@/hooks/useStepedRequest";
import { settingModel } from "@/stores/settingModel";
import { useCreation, useRequest } from "ahooks"
import dayjs from "dayjs";
import { useModel } from "foca";
import { useEffect } from "react";

const Bi01VisitorRatio = () => {

    const settingModelState = useModel(settingModel);
    const peopleCountRate = useCreation(() => settingModelState?.Admin?.peopleCountRate || 1, [settingModelState?.Admin?.peopleCountRate]);
    // refreshDeps: [peopleCountRate]

    const apiBiPeopleCountInAndOutReportSteped = useStepedRequest(apiBiPeopleCountInAndOutReport);


    const { data } = useRequest(async () => {
        const start = dayjs().format('YYYY-MM-DD HH');
        const end = dayjs().format('YYYY-MM-DD HH');
        const res = await apiBiPeopleCountInAndOutReportSteped({
            timeUnit: 'hour',
            start: start,
            end: end
        })

        const inCount = Math.round((res.in[0].data.reduce((pre, cur) => pre + cur.remainder, 0)) * peopleCountRate)
        const outCount = Math.round(res.out[0].data.reduce((pre, cur) => pre + cur.remainder, 0) * peopleCountRate)
        return [
            { value: inCount, name: '入场' },
            { value: outCount, name: '出场' },
        ]
    }, {
        pollingInterval: 60 * 60 * 1000,
        refreshDeps: [peopleCountRate],
    })

    const { chart, setContainerRef } = useEcharts({
        theme: 'walden',
    });

    useEffect(() => {
        if (chart && data) {
            chart.setOption({
                grid: {
                    left: 0,
                    right: 0,
                    bottom: 0,
                    top: 0,
                    containLabel: true
                },
                tooltip: {
                    show: false
                },
                series: [
                    {
                        name: '出入占比',
                        type: 'pie',
                        radius: ['20%', '70%'],
                        center: ['50%', '50%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                        },
                        label: {
                            color: '#C9E8F2ee',
                            formatter: (params: any) => {
                                return `{name|${params.data.name}} {per|${params.percent}%}\n{val|${params.value}}`
                            },
                            minMargin: 5,
                            edgeDistance: 5,
                            lineHeight: 18,
                            rich: {
                                per: {
                                    fontSize: 10,
                                    color: '#fff',
                                    backgroundColor: '#4C5058',
                                    padding: [3, 4],
                                },
                                val: {
                                    fontFamily: 'OPPOSans-H',
                                    color: 'inherit',
                                },
                            }
                        },
                        labelLine: {
                            show: true,
                            length: 20,
                            length2: 50,
                            maxSurfaceAngle: 80
                        },
                        emphasis: {
                            label: {
                                show: true
                            }
                        },
                        data: data.map(item => ({
                            ...item,
                            itemStyle: {
                                color: item.name === '入场' ? '#FDBF44' : '#00D3FD'
                            }
                        }))
                    }
                ]
            });
        }
    }, [chart, data]);

    return <div className="w-full h-full relative">
        <img className="absolute left-[195px] top-[10px]" src="/images/bi/pie_bg.webp" alt="" />
        <div ref={setContainerRef} className="w-full h-full"></div>
    </div>;
}

export default Bi01VisitorRatio;