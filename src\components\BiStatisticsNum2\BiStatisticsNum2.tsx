
export interface TBiStatisticsNum2Props {
  value?: string | number
  label: string
}

function BiStatisticsNum2(props: TBiStatisticsNum2Props) {
  return (
    <div className="BiStatisticsNum2 relative w-[160px] h-[157px] bg-no-repeat" style={{ backgroundImage: 'url(/images/bi/statistics_num2_bg.webp)', backgroundSize: '100% auto' }}>
      <div className="absolute top-[35px] w-full text-[#c9e8f2] text-center font-bold text-[28px]">{props.value ? props.value : '/'}</div>
      <div className="absolute top-[115px] w-full text-[#c9e8f2] text-center text-[12px]">{props.label}</div>
    </div>
  )
}

export default BiStatisticsNum2
