import { useEffect } from 'react';
import { useRequest } from 'ahooks';
import { ToolsUtil } from '@/utils/tools';
import { useEcharts } from '@/hooks/useEcharts';

const colorPairs = [
  ['#FF4B6B', '#FF86A4'], 
  ['#FF8E3C', '#FFB661'], 
  ['#3DB8FF', '#66CCFF'], 
  ['#FF3E3E', '#FF6666'],
  ['#FFD93D', '#FFE566'], 
  ['#4ECB73', '#7EE699'], 
];

const Bi01PlantingType = () => {
  const { data } = useRequest(async () => {
    await ToolsUtil.waitTime(100);

    // 玫瑰区(万枝)	康乃馨区(万枝)	百合区(万枝)	绣球区(万枝)	杂花区(万枝)
    // 382360 	206306 	48898 	9522 	344768 

    const baseData = [
      { name: '玫瑰', value: 382360 },
      { name: '康乃馨', value: 206306 },
      { name: '杂花', value: 344768 },
      { name: '百合', value: 48898 },
      { name: '绣球', value: 9522 },

    ]
    const total = baseData.reduce((pre, cur) => pre + cur.value, 0);
    return baseData.map((item, index) => ({
      ...item,
      value: Number((item.value / total * 100).toFixed(2)),
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: colorPairs[index][0]
          }, {
            offset: 1,
            color: colorPairs[index][1]
          }]
        }
      }
    }));
  });

  const { chart, setContainerRef } = useEcharts();

  useEffect(() => {
    if (chart && data) {
      chart.setOption({
        darkMode: true,
        legend: {
          orient: 'horizontal',
          bottom: 10,
          left: 'center',
          itemWidth: 8,
          itemHeight: 8,
          icon: 'circle',
          textStyle: {
            color: '#C9E8F2',
            fontSize: 10,
          },
          formatter: (name: string) => {
            const item = data.find(item => item.name === name);
            return `${name} ${item?.value}%`;
          },
          itemGap: 12,
          itemStyle: {
            borderWidth: 0
          },
          padding: [0, 0, 0, 0],
          width: '90%',
          selectedMode: false
        },
        series: [
          {
            type: 'pie',
            radius: ['20%', '70%'],
            center: ['50%', '40%'],
            roseType: 'area',
            itemStyle: {
              borderRadius: 4
            },
            label: {
              show: false
            },
            data: data
          }
        ]
      }, true);
    }
  }, [data, chart]);

  return <div ref={setContainerRef} className="w-full h-full" />;
};

export default Bi01PlantingType;
