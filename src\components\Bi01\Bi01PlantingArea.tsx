import { useEffect, useRef, useState } from 'react';
import { useCreation, useRequest } from 'ahooks';
import { useEcharts } from '@/hooks/useEcharts';
import BiStatisticsNum3 from '../BiStatisticsNum3';
import { apiBiTruckCount } from '@/apis/apis.api';

// const areaNames = [
//   '昆明市', '曲靖市', '玉溪市', '楚雄彝族自治州',
//   '红河哈尼族彝族自治州',
// ];

const stopCityMap = `
玉溪市,丁丽梅
昆明市,上蒜
昆明市,中和铺
昆明市,临时集货点
昆明市,二街
昆明市,云花物流
昆明市,余家海
昆明市,元山童汝雄
昆明市,八街
昆明市,六街
昆明市,六街新寨
玉溪市,刘合营
昆明市,大古城
昆明市,大河尾
昆明市,大麦地
昆明市,宜良中所
昆明市,宜良羊街
昆明市,富民
昆明市,富民大营
昆明市,富民赤鹫
玉溪市,峨山小街
昆明市,嵩明小新街
昆明市,嵩明小街
昆明市,新街
昆明市,昆阳
玉溪市,易门
昭通市,朱家营
昆明市,杨伟富民
昆明市,杨林四营
楚雄彝族自治州,武定
楚雄彝族自治州,武定九厂
楚雄彝族自治州,武定禄金
玉溪市,玉溪红塔
昆明市,石林
昆明市,禄劝
昆明市,禄劝翠华
昆明市,罗茨
玉溪市,通海
玉溪市,通海元山
玉溪市,通海四街
玉溪市,通海新义
玉溪市,通海玉佳园
昆明市,骄阳花卉
昆明市,鸡街
`.split('\n').reduce((before, item) => {
  const [city, area] = item.split(',');
  before[area] = city
  return before
}, {} as Record<string, string>)

const dounanCenter = [102.786371, 24.901604];

const Bi01PlantingArea = () => {

  const { chart, echarts, setContainerRef } = useEcharts({
    theme: 'default',
    options: {
      renderer: 'canvas',
    }
  })
  const { chart: chart2, setContainerRef: setContainerRef2 } = useEcharts()

  const timerRef = useRef<NodeJS.Timeout>(null)
  const selectedAreaRef = useRef<string | null>(null)
  const [selectedArea, setSelectedArea] = useState<string | null>(null);

  const { data: truckData } = useRequest(async () => {
    const res = await apiBiTruckCount();
    // console.log("r2", r2);
    // const response = await fetch('/jsons/truck.json');
    // const res = await response.json();
    const cityData: Record<string, number> = {}
    res.forEach((item) => {
      const city = stopCityMap[item.StopName];
      if(!cityData[city]) {
        cityData[city] = 0
      }
      cityData[city] += Number(item.count)
    })
    
    return cityData
  })

  const { data: plantAreaData } = useRequest(async () => {
    const response = await fetch('/jsons/plantArea.json');
    return await response.json();
  })

  const yearTotal = useCreation(() => {
    if (!plantAreaData) return 0
    return Math.round(plantAreaData.reduce((pre, cur) => pre + cur.plant, 0) * 10000)
  }, [plantAreaData])

  const data = useCreation( () => {
    if(!truckData) return [];
    return Object.keys(truckData || {}).map(name => ({
      name,
      value: Math.floor(Math.random() * 800 + 200),
      truckCount: (truckData || {})[name],

    }))
  }, [truckData]);

  const areaNameTruckCountMap = useCreation(() => {
    if (!data) return {};
    return data.reduce((before, item) => {
      before[item.name] = item.truckCount;
      return before;
    }, {} as Record<string, number>);
  }, [data]);

  const { data: geoJson } = useRequest(async () => {
    const response = await fetch('/jsons/530000.json');
    return await response.json();
  });

  const linesData = useCreation(() => {
    if (!geoJson) return [];
    return geoJson.features.filter(item => Object.keys(truckData || {}).includes(item.properties.name)).map(item => {
      const center = item.properties.center;
      return {
        coords: [center, dounanCenter]
      }
    })
  }, [geoJson, truckData])

  const tableData = useCreation(() => {
    if (!plantAreaData) return [];
    return plantAreaData.sort((a: any, b: any) => b.plant - a.plant).slice(0, 8).map(item => {
      return {
        name: item.city,
        value: item.plant,
        area: item.area,
      }
    })
  }, [plantAreaData])



  useEffect(() => {
    if (!chart || !geoJson || !data || !linesData) return;

    // 注册地图数据
    echarts.registerMap('yunnan', geoJson);

    const option = {
      // tooltip: {
      //   trigger: 'item',
      //   formatter: function (params: any) {
      //     return `${params.name}<br/>产量：${params.value}万枝<br/>发车：${params.data.truckCount}辆`
      //   }
      // },
      geo: {
        map: 'yunnan',
        aspectScale: 1,
        zoom: 0.55,
        layoutCenter: ["65%", "50%"],
        layoutSize: "180%",
        show: true,
        roam: false,
        label: {
          show: false,
          textStyle: { color: "#fff" },
        },
        emphasis: {
          focus: 'none'
        },
        selectedMode: false,
        silent: true,
        itemStyle: {
          borderColor: "#c0f3fb",
          borderWidth: 1,
          shadowColor: "#8cd3ef",
          shadowOffsetY: 10,
          shadowBlur: 120,
          areaColor: "transparent",
        },
      },
      series: [
        {
          type: "map",
          map: "yunnan", // 自定义扩展图表类型
          aspectScale: 1,
          zoom: 0.55, // 缩放
          showLegendSymbol: true,
          label: {
            show: true,
            textStyle: { color: "#fff" },
            formatter: function (params: any) {
              return params.name + (areaNameTruckCountMap[params.name] ? `\n\n${areaNameTruckCountMap[params.name]}辆` : '')
            }
          },

          selectedMode: false,
          silent: true,
          itemStyle: {
            areaColor: {
              type: "linear",
              x: 1200,
              y: 0,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(3,27,78,0.75)", // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "rgba(58,149,253,0.75)", // 50% 处的颜色
                },
              ],
              global: true, // 缺省为 false
            },
            borderColor: "#fff",
            borderWidth: 0.2,

          },
          emphasis: {
            focus: 'none',
            itemStyle: {
              areaColor: 'rgba(0, 255, 157, 0.6)'  // 修改为绿色
            },
            label: {
              show: true,
              textStyle: {
                color: '#fff',
              }
            }
          },
          layoutCenter: ["65%", "50%"],
          layoutSize: "180%",
          markPoint: {
            symbol: "none",
          },
          data: data,
        },
        {
          type: 'lines',
          zlevel: 2,
          effect: {
            show: true,
            period: 10, //箭头指向速度，值越小速度越快
            trailLength: 0, //特效尾迹长度[0,1]值越大，尾迹越长重
            symbol: 'image:///images/bi/truck.png',
            symbolSize: 24 //图标大小
          },
          lineStyle: {
            color: 'rgba(255, 200, 113, 0.5)',
            width: 1, //尾迹线条宽度
            opacity: 1, //尾迹线条透明度
            curveness: 0.1, //尾迹线条曲直度
            shadowColor: 'rgba(255, 200, 113, 0.2)',
            shadowBlur: 10
          },
          data: linesData,
        },
      ]
    };

    chart.setOption(option);
  }, [chart, geoJson, data, linesData]);

  useEffect(() => {
    if (!chart2 || !tableData) return;
    chart2.setOption({
      grid: {
        top: 10,
        right: 10,
        bottom: 20,
        left: -20,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: tableData.map(item => item.name),
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,0.2)'
          }
        },
        axisLabel: {
          color: '#91E4FF',
          fontSize: 12,
          interval: 0,  // 强制显示所有标签
          rotate: 45    // 文字倾斜45度
        }
      },
      yAxis: {
        type: 'value',
        show: false,
        splitLine: {
          show: false
        }
      },
      series: [
        {
          data: tableData.map(item => item.value),
          type: 'bar',
          barWidth: 20,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: 'rgba(0,254,233,0.8)'
              }, {
                offset: 1,
                color: 'rgba(0,254,233,0.2)'
              }]
            }
          }
        }
      ]
    });
  }, [chart2, tableData])

  useEffect(() => {
    if(!truckData) return;
    const areaNames = Object.keys(truckData);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    timerRef.current = setInterval(() => {
      const index = areaNames.indexOf(selectedAreaRef.current);
      const nextIndex = (index + 1) % areaNames.length;
      selectedAreaRef.current = areaNames[nextIndex];
      setSelectedArea(areaNames[nextIndex]);
    }, 1000 * 5)
  }, [truckData])




  useEffect(() => {
    // 高亮
    if (!chart || !data) return;

    // 先取消之前的高亮
    chart.dispatchAction({
      type: 'downplay',
      seriesIndex: 0
    });

    const dataIndex = data.findIndex(item => item.name === selectedAreaRef.current);

    chart.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex,
    });
    // chart.dispatchAction({
    //   type: 'showTip',
    //   seriesIndex: 0,
    //   dataIndex,
    // });

  }, [chart, data, selectedArea])

  return (
    <div className="relative w-full h-full">
      <div className="absolute left-0 top-20px  w-320px flex justify-center">
        <BiStatisticsNum3 value={yearTotal} size={30} label="年产量（万枝）" />
      </div>
      <div className='absolute z-10 left-2 top-[100px] w-[320px] h-[200px]' ref={setContainerRef2}></div>
      <div className="absolute z-10 left-2 top-[310px] w-[320px] text-xs">
        <table className="w-[320px] border-collapse text-[#91E4FF] rounded overflow-hidden" style={{
          background: 'linear-gradient(180deg, rgba(24, 144, 255, 0.05) 0%, rgba(24, 144, 255, 0.02) 100%)',
          backdropFilter: 'blur(2px)'
        }}>
          <thead>
            <tr style={{ background: '#0D4E7388' }} >
              <th className="p-1.5 px-3 text-left font-bold">地区</th>
              <th className="p-1.5 text-left  font-bold">产量</th>
              <th className="p-1.5 text-left  font-bold">面积</th>
            </tr>
          </thead>
          <tbody>
            {(tableData || []).map((item, index) => (
              <tr
                key={index}
              >
                <td className="p-1.5 px-3 text-left border-b border-white/10">{item.name}</td>
                <td className="p-1.5 text-left text-[#07C3FF]">{item.value}亿枝</td>
                <td className="p-1.5 text-left text-[#07C3FF]">{item.area}万亩</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="relative w-full h-full" ref={setContainerRef} />
    </div>
  );
};

export default Bi01PlantingArea;
