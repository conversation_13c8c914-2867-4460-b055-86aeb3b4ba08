import { useModel } from "foca";
import { settingModel } from "@/stores/settingModel";
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination, Navigation } from 'swiper/modules';
import 'swiper/css';
import { useCreation } from "ahooks";
import { useAsyncEffect, useGetState, useMemoizedFn } from "ahooks";
import { useEffect, useRef } from "react";
import waitUntil from "async-wait-until";
import dayjs from "dayjs";

import { ToolsUtil } from "@/utils/tools";

const username = 'admin'
const password = 'admin@123'
const maxChannel = 99

const Bi01TradeMonitor = () => {

    const settingModelState = useModel(settingModel);

    const [channelList, setChannelList, getChannelList] = useGetState<{
        name: string,
        id: number,
    }[]>([])

    console.log('channelList', window.location.hostname)

    const host = useCreation(() => {
        if (window.location.port === '10019') {
            return '********:7045'
        }

        return settingModelState.Admin?.buildingNvrAddress
    }, [settingModelState.Admin?.buildingNvrAddress])



    const [startLive, endLive] = useCreation(() => {
        return settingModelState.Admin?.buildingNvrLiveRange || ['18:00', '20:00']
    }, [settingModelState.Admin?.buildingNvrLiveRange])

    // const [resetPlayerId, setResetPlayerId] = useState(0);
    // const [startLive, setStartLive] = useLocalStorageState('playground-startLive', {
    //     defaultValue: '12:00',
    // });
    // const [endLive, setEndLive] = useLocalStorageState('playground-endLive', {
    //     defaultValue: '14:30',
    // });
    // const [timeRange, setTimeRange] = useState<{ startTime: string, endTime: string }>({})

    // 检查当前是否是直播时间
    const checkIsLiveTime = useMemoizedFn(() => {
        const now = dayjs();
        const currentTime = now.format('HH:mm');
        const isInRange = (currentTime >= startLive && currentTime <= endLive);
        return isInRange;
    });




    const videoMapRef = useRef([])
    const canvasMapRef = useRef([])
    const playerInstancesRef = useRef([]);

    const [isLiveTime, setIsLiveTime, getIsLiveTime] = useGetState<boolean>(checkIsLiveTime());


    const playerTime = useCreation(() => {
        const todayStartLive = dayjs().format(`YYYY-MM-DD ${startLive}:00`);
        const isBeforeTodayStartLive = dayjs().isBefore(todayStartLive);
        const day = isBeforeTodayStartLive ? dayjs().subtract(1, 'day').format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD');
        return (isLiveTime ? '直播' : '回放') + ` ${day} ${startLive}~${endLive}`
    }, [startLive, endLive, isLiveTime])

    useEffect(() => {
        const interval = setInterval(() => {
            const newIsLiveTime = checkIsLiveTime();
            setIsLiveTime(newIsLiveTime)
        }, 1000)
        return () => {
            clearInterval(interval)
        }
    }, [startLive, endLive])

    // 停止所有播放器
    const stopAllPlayers = useMemoizedFn(() => {
        playerInstancesRef.current.forEach(player => {
            player && player.stop();
            player && player.close();
        });
        playerInstancesRef.current = [];
    });


    useEffect(() => {
        const timer = setTimeout(() => {
            const newIsLiveTime = checkIsLiveTime();
            if (newIsLiveTime !== getIsLiveTime()) {
                setIsLiveTime(newIsLiveTime);
            }
        }, 1000)
        return () => {
            clearTimeout(timer)
            stopAllPlayers();
        }
    }, [])


    useAsyncEffect(async () => {
        console.log('重新加载播放')
        // 停止所有当前播放
        stopAllPlayers();
        await ToolsUtil.waitTime(2000);
        if (!channelList.length) return
        await waitUntil(() => videoMapRef.current.length && canvasMapRef.current.length, 10 * 1000)
        if (getIsLiveTime()) {
            console.log('直播模式')
            // 直播模式
            startLives();
        } else {
            console.log('回放模式')
            // 回放模式
            startPlaybacks();
        }
    }, [channelList, isLiveTime])


    const startPlaybacks = useMemoizedFn(async () => {
        const todayStartLive = dayjs().format(`YYYY-MM-DD ${startLive}:00`);

        const isBeforeTodayStartLive = dayjs().isBefore(todayStartLive);
        console.log('todayStartLive', isBeforeTodayStartLive)
        const day = isBeforeTodayStartLive ? dayjs().subtract(1, 'day').format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD');
        const startTime = `${day} ${startLive}:00`;
        const endTime = `${day} ${endLive}:00`;
        for (let i = 0; i < channelList.length; i++) {

            if (i >= maxChannel) { // TODO
                break
            }

            const channel = channelList[i]
            const records = await searchRecords(startTime, endTime, channel.id);
            let recordIndex = 0;
            const playNextRecord = () => {
                let playerInstance = playerInstancesRef.current[channel.id];
                if (playerInstance) {
                    playerInstance.stop();
                    playerInstance.close();
                    playerInstance = null;
                }
                const record = records[recordIndex];
                let range = 0;
                if (recordIndex === 0) {
                    range = Math.round((dayjs(startTime).valueOf() - dayjs(record.StartTime).valueOf()) / 1000);
                }
                playerInstance = new PlayerControl({
                    wsURL: `ws://${host}/rtspoverwebsocket`,
                    rtspURL: `rtsp://${host}/${record.FilePath}`,
                    username: username,
                    password: password,
                    lessRateCanvas: true,
                    playback: true,
                    range: range,
                    isPrivateProtocol: false,
                    realm: RPC.realm, //设备登录返回的realm 
                    playbackIndex: 1,
                    h265AccelerationEnabled: true, // 硬解码是否开启，默认不开启
                });
                let end = Math.floor(
                    Math.min(dayjs(record.EndTime).valueOf(), dayjs(endTime).valueOf()) / 1000
                );
                playerInstance.on('UpdateTimeStamp', (e) => {
                    if (e.timestamp >= end - 1) {
                        console.log(channel, 'playNextRecord')
                        recordIndex = (recordIndex + 1) % records.length;
                        end = Math.floor(
                            Math.min(dayjs(records[recordIndex].EndTime).valueOf(), dayjs(endTime).valueOf()) / 1000
                        );
                        playNextRecord();
                    }
                })
                playerInstance.on('WorkerReady', function () {
                    playerInstance.connect();
                });
                playerInstance.on('DecodeStart', function (rs: any) {
                    console.log('DecodeStart', rs);
                    if (rs?.decodeMode === 'canvas') {
                        canvasMapRef.current[i].style.display = 'block';
                        videoMapRef.current[i].style.display = 'none';
                    } else {
                        canvasMapRef.current[i].style.display = 'none';
                        videoMapRef.current[i].style.display = 'block';
                    }
                });
                playerInstance.init(canvasMapRef.current[i], videoMapRef.current[i]);
                playerInstancesRef.current[i] = playerInstance;
            }
            playNextRecord();
        }
    })

    // 查询录像记录
    const searchRecords = useMemoizedFn((startTime: string, endTime: string, channelId: number) => {
        // 查找下一个文件
        const findNextFile = (queryId: string) => {
            return new Promise((resolve, reject) => {
                RPC.MediaFileFind.findNextFile(queryId, { count: 100 })
                    .then(data => {
                        if (data.found > 0) {
                            resolve(data.infos);
                        } else {
                            resolve([]);
                        }
                    })
                    .catch(err => reject(err));
            });
        };

        return new Promise<{
            index: number,
            Channel: number,
            Cluster: number,
            CutLength: number,
            Disk: number,
            EndTime: string,
            FilePath: string,
            Flags: string[],
            Length: number,
            Partition: number,
            StartTime: string,
            Type: string,
            VideoStream: string
        }[]>((resolve, reject) => {
            RPC.MediaFileFind.instance().then(json => {
                const queryId = json.result;
                const params = {
                    condition: {
                        Channel: channelId,
                        StartTime: startTime,
                        EndTime: endTime,
                        Types: ['dav', 'mp4'],
                    }
                };

                RPC.MediaFileFind.findFile(queryId, params)
                    .then(() => findNextFile(queryId))
                    .then(records => resolve(records))
                    .catch(err => reject(err));
            });
        });
    });


    // 开始所有通道的直播
    const startLives = useMemoizedFn(() => {
        console.log('开始直播', channelList)
        for (let i = 0; i < channelList.length; i++) {
            if (i >= maxChannel) {
                return
            }
            const channel = channelList[i]
            const playerInstance = new PlayerControl({
                wsURL: `ws://${host}/rtspoverwebsocket`,
                rtspURL: `rtsp://${host}/cam/realmonitor?channel=${channel.id + 1}&subtype=0&proto=Private3`,
                username: username,
                password: password,
                lessRateCanvas: true,
                // playback: isPlayback, 
                isPrivateProtocol: false,
                realm: RPC.realm, //设备登录返回的realm
                // playbackIndex: playbackIndex, 
                h265AccelerationEnabled: true, // 硬解码是否开启，默认不开启
            });

            playerInstance.on('WorkerReady', function () {
                playerInstance.connect();
                console.log('WorkerReady', channel.id, channel.name);
            });
            playerInstance.on('DecodeStart', function (rs: any) {
                console.log('DecodeStart', rs);
                if (rs?.decodeMode === 'canvas') {
                    canvasMapRef.current[i].style.display = 'block';
                    videoMapRef.current[i].style.display = 'none';
                } else {
                    canvasMapRef.current[i].style.display = 'none';
                    videoMapRef.current[i].style.display = 'block';
                }
            });
            playerInstance.init(canvasMapRef.current[i], videoMapRef.current[i]);
            playerInstancesRef.current[i] = playerInstance;


        }
    });

    // useEffect(() => {
    //     console.log('resetPlayerId', resetPlayerId, 'activeIndex', activeIndex)
    // }, [resetPlayerId, activeIndex])

    // useEffect(() => {
    //   const interval = setInterval(() => {
    //     if (!getChannelList().length) return
    //     const newActiveIndex = (getActiveIndex() + 1) % (getChannelList().length)
    //     setActiveIndex(newActiveIndex)
    //     getChannelList().forEach((item, index) => {
    //       const playerInstance = playerInstancesRef.current.get(item.id);
    //       if (!playerInstance) return;
    //       if (index === newActiveIndex) {
    //         playerInstance.play();
    //       } else {
    //         playerInstance.pause();
    //       }
    //     })

    //   }, 20000)
    //   return () => {
    //     clearInterval(interval)
    //   }
    // }, [])



    useAsyncEffect(async () => {
        if (!host) return
        await waitUntil(() => !!host, 30 * 1000)
        setIP(host)
        const loginRes = await RPC.login(username, password, true)
        setCookie('DWebClientSessionID', loginRes.session, -1);
        setCookie('DhWebClientSessionID', loginRes.session, -1);
        RPC.keepAlive(300, 60000, _getSession(), host)
        const res = await RPC.LogicDeviceManager.getCameraAll()
        const channelList = res.camera
            .filter(item => item.Enable)
            .map(item => {
                return {
                    name: item.DeviceInfo.VideoInputs.find(input => input.Enable).Name,
                    id: item.UniqueChannel
                }
            })
        setChannelList(channelList)
    }, [host]);


    return (
        <div className="w-full h-full flex justify-between">

            <Swiper
                // style={{ height: '232px' }}
                modules={[Autoplay, Pagination, Navigation]}
                spaceBetween={50}
                slidesPerView={3}
                autoplay={{
                    delay: 20 * 1000,
                    disableOnInteraction: false
                }}
                loop
                observer
                observeParents
                onSlideChange={(swiper) => {
                    const newActiveIndex = swiper.activeIndex;
                    // setActiveIndex(newActiveIndex)
                    getChannelList().forEach((item, index) => {
                        const playerInstance = playerInstancesRef.current[index];
                        if (!playerInstance) return;
                        const longIndex = index < newActiveIndex ? index + channelList.length : index
                        const isActive = longIndex === newActiveIndex || longIndex === newActiveIndex + 1 || longIndex === newActiveIndex + 2
                        if (isActive) {
                            playerInstance.play();
                        } else {
                            playerInstance.pause();
                        }
                    })

                }}
            // //   onSwiper={(swiper) => console.log(swiper)}
            >
                {channelList.map((item, index) => (
                    <SwiperSlide key={item.id}>
                        <div className="w-416px text-center text-cyan-100 mb-10px flex justify-between px-10px">
                            <span>{item.name}</span>
                            <span>{playerTime}</span>
                        </div>
                        <div className="w-416px h-232px bg-black overflow-hidden rounded-md">
                            <video style={{ width: '100%', height: '100%', display: 'none' }} ref={ref => videoMapRef.current[index] = ref}></video>
                            <canvas style={{ width: '100%', height: '100%' }} ref={ref => canvasMapRef.current[index] = ref}></canvas>
                        </div>
                    </SwiperSlide>
                ))}
            </Swiper>

        </div>
    );
};

export default Bi01TradeMonitor;
