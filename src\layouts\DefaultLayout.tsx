import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { AliveScope } from "react-activation";
import { useEffect, useMemo, useState } from 'react';
import { Avatar, Dropdown, Menu, Space, Badge } from 'antd';
import type { MenuProps } from 'antd';
import { TLayoutRoute, layoutRoute } from "@/router";
import { useCreation, useMemoizedFn } from 'ahooks';
import { nanoid } from '@ant-design/pro-components';
import { useModel } from 'foca';
import { authModel } from '@/stores/authModel';
import { BellOutlined, UserOutlined } from '@ant-design/icons';
import TabNav from '@/components/TabNav';
import { layoutModel } from '@/stores/layoutModel';


type MenuItem = Required<MenuProps>['items'][number] & {
  [key: string]: any;
}

const DefaultLayout = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const authModelState = useModel(authModel);
  const layoutModelState = useModel(layoutModel);
 

  const mainWidth = useMemo(() => {
    return layoutModelState.windowWidth - layoutModelState.sidebarWidth;
  }, [layoutModelState.windowWidth, layoutModelState.sidebarWidth])

  const mainHeight = useMemo(() => {
    return layoutModelState.windowHeight - layoutModelState.headerHeight - layoutModelState.navbarHeight;
  }, [layoutModelState.windowHeight])

  const [menuOpenKeys, setMenuOpenKeys] = useState<string[]>([])
  const [menuSelectedKeys, setMenuSelectedKeys] = useState<string[]>([])


  const menuKeysMap = useCreation(() => new Map<string, string[]>(), [])
  // const menuParentKeysMap = useCreation(() => new Map<string, string[]>(), [])
  const menuKeyLinkMap = useCreation(() => new Map<string, string>(), [])

  const menuItems: MenuItem[] = useCreation(() => {
    const initMenuOpenKeys: string[] = []
    const transform = (items: TLayoutRoute[], parentKeys: string[] = []): MenuItem[] => {
      const result: MenuItem[] = []
      items.forEach(item => {
        if (!item.hideInMenu) {
          const key = nanoid()
          const children = item.routes ? transform(item.routes, [...parentKeys, key]) : []
          
          // 收集子菜单的badgeCountKeys
          if (children.length > 0) {
            initMenuOpenKeys.push(key)
            // 合并所有子菜单的badgeCountKeys
           
          }
          
          // 去重
          
          let label: any = item.name


          // 判断当前项是否可以访问
          
          // 如果当前项没有 auth 属性，且有子菜单，则其是否显示由所有子菜单是否有一个可显示来决定
      
          
          result.push({
            key,
            link: item.path,
            icon: item.icon,
            label,
            children: children.length > 0 ? children : undefined,
          })
          menuKeysMap.set(item.path, [...(menuKeysMap.get(item.path) || []), key])
          menuKeyLinkMap.set(key, item.path)
        } else {
          menuKeysMap.set(item.path, parentKeys)
        }
      })
      return result
    }
    menuKeysMap.clear()
    return transform(layoutRoute.routes || [])
  }, [])

  useEffect(() => {
    setMenuSelectedKeys(menuKeysMap.get(location.pathname) || [])
  }, [menuItems, location.pathname])



  // const {data: showMessageDot} =  useRequest(async () => {
  //   const res = await apiMessagePageList({
  //     pageNum: 1,
  //     pageSize: 1,
  //     filter: [{ field: 'read', type: 'eq', value: false }],
  //   })
  //   return res.total > 0
  // }, {
  //   pollingInterval: 1000 * 60,
  // })



  return (authModelState.account ? (
    <div className="w-screen h-screen overflow-hidden flex flex-col items-stretch">
      <div
        style={{ borderBottom: '1px solid #eee', height: layoutModelState.headerHeight }}
        className='shrink-0 flex items-center px-[20px] border-t-2 border-blue-900'
      >
        <div className='text-[18px] font-bold ml-[10px]'>
          {import.meta.env.VITE_APP_TITLE}
        </div>
        <div className="ml-auto"></div>
        <Dropdown menu={{
          items: [
            { key: 'logout', label: '退出登录' }
          ],
          onClick: (item) => {
            switch (item.key) {
              case 'logout':
                authModel.logout()
                break;
              default:
            }
          },
        }} placement="bottomRight">
          <Space className='cursor-pointer'>
            <Avatar size="small" icon={<UserOutlined />} />
            <span>{authModelState.account?.nickname || ''}</span>
          </Space>
        </Dropdown>
      </div>
      <div className='flex-1 flex'>
        <div className='shrink-0 overflow-auto h-[calc(100vh-60px)]' style={{ width: layoutModelState.sidebarWidth }}>
          <Menu
            onOpenChange={(keys) => setMenuOpenKeys(keys)}
            onSelect={(e) => setMenuSelectedKeys(e.selectedKeys)}
            selectedKeys={menuSelectedKeys}
            openKeys={menuOpenKeys}
            mode="inline"
            inlineCollapsed={false}
            items={menuItems}
            onClick={(e) => {
              const link = menuKeyLinkMap.get(e.key)
              link && navigate(link)
            }}
          />
        </div>
        <div className='flex-1'>
          <div style={{
            height: layoutModelState.navbarHeight,
            width: mainWidth,
            borderBottom: '1px solid #eee'
          }}>
            <TabNav />
          </div>

          <div className='bg-[#eee] overflow-auto p-[20px]'
          id="main-content"
            style={{
              height: mainHeight,
              width: mainWidth,
            }}
          >
            <AliveScope>
              <Outlet />
            </AliveScope>

          </div>

        </div>
      </div>
    </div>
  ) : null
  );
}

export default DefaultLayout;