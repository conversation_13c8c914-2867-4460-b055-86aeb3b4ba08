import { defineModel } from 'foca';
import { waitUntil } from 'async-wait-until';
import { HttpUtils } from '@/utils/http';
import CacheUtils from '@/utils/cache';
import router from '@/router';
import { apiLogin, apiGetCurrentUser } from '@/apis/apis';
import dayjs from 'dayjs';

export interface AuthModelState {
  account?: TAdminUser;
  logined: boolean;
  permissions: string[];
}

const initialState: AuthModelState = {
  account: undefined,
  logined: false,
  permissions: [],
};


export const authModel = defineModel('auth', {
  initialState,
  computed: {},
  reducers: {},
  methods: {
    async loginByPwd(...[data]: Parameters<typeof apiLogin>) {
      try {
        const res = await apiLogin(data);
 
        this.afterLoadAccount(res);
      } catch (error) {
        throw new Error('登录失败');
        /* empty */
      } finally {
        this.setState({ logined: true });
      }
    },

    async getAccount() {
      try {
        const res = await apiGetCurrentUser();
        this.afterLoadAccount({user: res});
        return res;
      } catch (error) {
        /* empty */
      } finally {
        this.setState({ logined: true });
      }
    },
    /**
     * 登录状态检查，否则跳转到登录页
     * @param waitTime 等待时间
     */
    async loginCheck(waitTime = 0) {
      try {
        await waitUntil(() => this.state.logined, waitTime);
        if (!this.state.account) {
          throw Promise.reject('未登录');
        }
      } catch (error) {
        router.navigate('/login', {
          replace: true,
        });
      }
    },

    logout() {
      HttpUtils.setToken('');
      CacheUtils.removeItem('ACCESS_TOKEN');
      this.setState({
        account: undefined,
        authCodes: [],
      });
      router.navigate('/login', {
        replace: true,
      });
    },
    afterLoadAccount(data: { token?: string; expiredAt: string; user: TAdminUser }) {
      const { token, expiredAt, user } = data;
     
      if (token != undefined) {
        const expiredAtNum = dayjs(expiredAt).valueOf();
        HttpUtils.setToken(token, expiredAtNum);
      }
      this.setState({
        account: user,
      });
    },
   
  },
  events: {
    onInit() {
      this.getAccount();
    },
  },
});
