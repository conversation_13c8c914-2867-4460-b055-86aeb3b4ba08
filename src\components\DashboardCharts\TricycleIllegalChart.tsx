import { apiTricycleIllegalCountList } from "@/apis/apis.api";
import { useEcharts } from "@/hooks/useEcharts";
import { ProCard } from "@ant-design/pro-components";
import { useRequest } from "ahooks";
import dayjs from "dayjs";
import { useEffect } from "react";

type TricycleIllegalChartProps = {

};

export const TricycleIllegalChart = ({ }: TricycleIllegalChartProps) => {

  const { data } = useRequest(async () => {
    const res = await apiTricycleIllegalCountList({
      dateRange: [
        dayjs().subtract(30, 'days').startOf('day').valueOf(),
        dayjs().add(1, 'days').startOf('day').valueOf(),
      ]
    })
    return res
  }, {
    pollingInterval: 60 * 1000,
  })

  const { chart: chart1, setContainerRef: setContainerRef1 } = useEcharts({
    theme: 'light',
  });
  const { chart: chart2, setContainerRef: setContainerRef2 } = useEcharts({
    theme: 'light',
  });


  useEffect(() => {
    if (chart2 && data) {
      const pieData = data.tricycleIllegalRules

      chart1.setOption({
        color:['#9FE080','#FDD75E','#FF7070','#73C0DE', '#5470C6'],
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c} ({d}%)'
        },
        legend: {
          top: 0,
          left: 'center'
        },
        series: [

          {
            type: 'pie',
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
           
            data: pieData
          },
        ]
      })
    }
  }, [chart1, data])
  useEffect(() => {
    if (chart2 && data) {
      const xAxisData = data.list.map((item) => item.date);
      const y1AxisData = data.list.map((item) => item.tricycleIllegalAdd);
      const y2AxisData = data.list.map((item) => item.tricycleIllegalFinePaid / 100);

      chart2.setOption({
        grid: {
          left: 40,
          right: 40,
          bottom: 40,
          top: 30,

        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData
        },
        yAxis: [
          {
            name: '新增违规',
            type: 'value',
            alignTicks: true,
            nameTextStyle: {
              color: '#fdb306',
            },
          },
          {
            name: '当日罚款缴纳',
            type: 'value',
            alignTicks: true,
            nameTextStyle: {
              color: '#02c30b',
            },
          }
        ],
        series: [
          {
            name: '新增违规',
            type: 'bar',
            yAxisIndex: 0,
            color: '#FFB92C',
            // barWidth: 18,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [{
                  offset: 0, color: '#80cc8388'
                }, {
                  offset: 1, color: '#80cc83'
                }],
              }
            },
            data: y1AxisData,
          },
          {
            name: '缴纳罚款',
            type: 'bar',
            yAxisIndex: 1,
            color: '#FFB92C',
            // barWidth: 18,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [{
                  offset: 0, color: '#FFB92C66'
                }, {
                  offset: 1, color: '#FFB92C'
                }],
              }
            },
            data: y2AxisData,
          },
        ]
      })
    }
  }, [chart2, data])

  return (
    <>
      <ProCard title="三轮车违规罚款" bordered className="flex-[2]">
        <div className="h-300px" ref={setContainerRef2}>
        </div>
      </ProCard>
      <ProCard title="三轮车违规类型" bordered className="flex-1">
        <div className="h-300px" ref={setContainerRef1}>
        </div>
      </ProCard>
    </>
  );
}
