import { createRef, useMemo } from "react";
import ReactD<PERSON> from "react-dom";
import FormilyModal, { TFormilyModalRef } from "./FormilyModal";

export function createFormilyModal<TFormData extends object>(id?: string) {
  const domId = id || 'formily-modal-' + Math.random().toString(36).slice(2, 9);
  const container = document.createElement('div');
  container.id = domId;
  document.body.appendChild(container);
  const ref = createRef<TFormilyModalRef<object>>();
  const formModalHolder = ReactDOM.createPortal(<FormilyModal formId={domId} ref={ref} />, container);
  return {
    formModalRef: ref as unknown as React.RefObject<TFormilyModalRef<TFormData>>,
    formModalHolder,
  };
}

export function useFormModal<TFormData extends object>() {
  return useMemo(() => createFormilyModal<TFormData>(), []);
}