import {
  connect,
  mapReadPretty,
} from '@formily/react'
import { useGetState } from 'ahooks';
import { Button, Input, Switch, Tree } from 'antd';
import { produce } from 'immer';
import { nanoid } from 'nanoid';
import lodashGet from 'lodash/get';
import { useEffect } from 'react';


const getTreeNodePath = (treeData: TDictOption[], key: string, path = []) => {
  for (let i = 0; i < treeData.length; i++) {
    const node = treeData[i];
    if (node.key === key) {
      return [...path, i];
    }
    if (node.children) {
      const childPath = getTreeNodePath(node.children, key, [...path, i, 'children']);
      if (childPath) {
        return childPath;
      }
    }
  }
  return null;
}

const getParentsTreeKeys = (treeData: TDictOption[]) => {
  const keys: string[] = [];
  for (let i = 0; i < treeData.length; i++) {
    const node = treeData[i];
    keys.push(node.key)
    if (node.children && node.children.length > 0) {
      keys.push(...getParentsTreeKeys(node.children))
    }
  }
  return keys;
}


const DictOptions = (props: {
  value?: TDictOption[];
  onChange: (value?: TDictOption[]) => void;
}) => {
  const [treeData, setTreeData, getTreeData] = useGetState<TDictOption[]>([])
  const [treeExpandedKeys, setTreeExpandedKeys, getTreeExpandedKeys] = useGetState<string[]>([])

  useEffect(() => {
    const newValue = JSON.parse(JSON.stringify(props.value || []))
    setTreeData(newValue)
    setTreeExpandedKeys(getParentsTreeKeys(newValue))
  }, [props.value])

  const syncToProps = () => {
    setTimeout(() => {
      props.onChange(getTreeData())
    }, 100)
  }

  const onNodeChange = (node: TDictOption, key: string, value: any) => {
    const path = getTreeNodePath(getTreeData(), node.key, [])
    setTreeData(produce(draft => {
      lodashGet(draft, path)[key] = value
    }))
    syncToProps()
  }

  const addNode = (parentKey?: string) => {
    const key = nanoid()
    console.log(key)
    const newNode: TDictOption = {
      key: key,
      label: '新选项',
      value: '1',
      children: [],
      status: true,
    }
    if (!parentKey) {
      setTreeData([...getTreeData(), newNode])
      syncToProps()
    } else {
      const path = [...getTreeNodePath(getTreeData(), parentKey!, []), 'children'] as any
      setTreeData(produce(draft => {
        lodashGet<any>(draft, path).push(newNode)
      }))
      if (getTreeExpandedKeys().indexOf(parentKey!) === -1) {
        setTreeExpandedKeys([...getTreeExpandedKeys(), parentKey!])
      }
    }
    syncToProps()
  }

  const onNodeStatusChange = (node: TDictOption, newStatus: boolean) => {
    const path = getTreeNodePath(getTreeData(), node.key, [])
    if(!path) {
      return
    }
    console.log(node, path, newStatus)
    if (newStatus) {
      setTreeData(produce(draft => {
        lodashGet(draft, path).status = true
      }))
    } else {
      const newNode = JSON.parse(JSON.stringify(node))
      const deepFalse = (_node: TDictOption) => {
        _node.status = false
        if (_node.children) {
          _node.children.forEach(n => deepFalse(n)
          )
        }
      }
      deepFalse(newNode)
      setTreeData(produce(draft => {
        lodashGet(draft, path).status = false
        lodashGet(draft, path).children = newNode.children
      }))
    }

    syncToProps()
  }

  // const deleteNode = (node: TDictOption) => {
  //   const path = getTreeNodePath(getTreeData(), node.key, [])
  //   const index = path.pop()
  //   if (path.length === 0) {
  //     setTreeData(produce(draft => {
  //       draft.splice(index, 1)
  //     }))
  //   } else {
  //     setTreeData(produce(draft => {
  //       lodashGet<any>(draft, path).splice(index, 1)
  //     }))
  //   }
  //   syncToProps()
  // }

  return (
    <div>
      <Tree
        className="ArticleCategoryEditor "
        showLine={{ showLeafIcon: true }}
        blockNode
        treeData={treeData}
        expandedKeys={treeExpandedKeys}
        onExpand={(keys) => setTreeExpandedKeys(keys as string[])}
        selectable={false}
        titleRender={(node: TDictOption) => (
          <div className="flex items-center gap-2 p-2">
            <Input
              defaultValue={node.label}
              onChange={(e) => onNodeChange(node, 'label', e.target.value)}
              onClick={e => e.stopPropagation()}
              allowClear
              placeholder='选项名称(中文，同级唯一)'
              disabled={!node.status}
            />
            <Input
              defaultValue={node.value}
              onChange={(e) => onNodeChange(node, 'value', e.target.value)}
              onClick={e => e.stopPropagation()}
              allowClear
              placeholder='选项值(数字或英文，同级唯一)'
              disabled={!node.status}
            />
            <Switch value={node.status} onChange={(v) => onNodeStatusChange(node, v)} />
            <Button disabled={!node.status} onClick={(e) => {
              e.stopPropagation()
              addNode(node.key)
            }} >新增</Button>
            {/* <Button title="删除" onClick={(e) => {
              e.stopPropagation()
              deleteNode(node)
            }} >删除</Button> */}
          </div>
        )}
      />
      <Button className='w-full mt-2' onClick={() => addNode()}>新增</Button>
    </div>
  );
};

export default connect(
  DictOptions,
  mapReadPretty(() => null),
);
