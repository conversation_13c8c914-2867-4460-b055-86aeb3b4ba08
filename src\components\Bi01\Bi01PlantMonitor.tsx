import { useModel } from "foca";
import BiVideoMonitor from "../BiVideoMonitor";
import { settingModel } from "@/stores/settingModel";


const Bi01PlantMonitor = () => {
    const videos = useModel(settingModel, (state) => state.Admin?.plantingAreaVideos || []);

    return (
        <div className="w-full h-full pt-10px">
            <div className="m-auto w-[576px]">
                <BiVideoMonitor videoSources={videos} />
            </div>

        </div>
    );
};

export default Bi01PlantMonitor;