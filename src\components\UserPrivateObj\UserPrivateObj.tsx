import { CopyOutlined } from "@ant-design/icons";
import { App, Dropdown } from "antd";

export interface TUserPrivateObjProps {
  userPrivateObj: TUserPrivate;
  children?: React.ReactNode;
}

function UserPrivateObj(props: TUserPrivateObjProps) {
  const { message } = App.useApp();
  return (
    props.userPrivateObj ? (
      <Dropdown menu={{
        items: [
          { icon: <CopyOutlined />, label: `姓名: ${props.userPrivateObj.realname}`, key: 'realname' },
          { icon: <CopyOutlined />, label: `手机号: ${props.userPrivateObj.mobile}`, key: 'mobile' },
          { icon: <CopyOutlined />, label: `身份证: ${props.userPrivateObj.idCard}`, key: 'idCard' },
          { icon: <CopyOutlined />, label: `地址: ${props.userPrivateObj.address}`, key: 'address' },
        ],
        onClick: (item) => {
          navigator.clipboard.writeText(props.userPrivateObj[item.key])
          message.success('复制成功')
        }
      }}
        arrow
      >
        <div className="cursor-pointer">
          {props.children ? props.children : <div className="text-blue-500">{props.userPrivateObj.realname}</div>}
        </div>
      </Dropdown>
    ) : null

  )
}

export default UserPrivateObj
