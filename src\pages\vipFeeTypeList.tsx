import { apiVipFeeTypePageList, apiVipFeeTypeUpdate } from "@/apis/apis";
import { useVipFeeTypeFormModal } from "@/hooks/useVipFeeTypeFormModal";
import { ToolsUtil } from "@/utils/tools";
import { ActionType, ProTable } from "@ant-design/pro-components";
import { Button, Space, Switch } from "antd";
import { useRef } from "react";
import { useNavigate } from "react-router-dom";

function VipFeeTypeList() {
  const tableRef = useRef<ActionType>();
  const navigate = useNavigate();
  const { showEditModal, formModalHolder } = useVipFeeTypeFormModal(() => {
    tableRef.current?.reload();
  });

  return (
    <div>
      <ProTable<TVipFeeType>
        headerTitle="会员费类型列表"
        actionRef={tableRef}
        rowKey="id"
        search={{
          defaultCollapsed: false,
        }}
        toolBarRender={() => [
          <Button key="create" type="primary" onClick={() => showEditModal()}>
            新建会员费类型
          </Button>,
        ]}
        pagination={{
          pageSize: 20,
          showQuickJumper: true,
        }}
        columns={[
          { dataIndex: 'id', title: 'ID', search: true },
          { dataIndex: 'name', title: '名称', search: true },
          {
            dataIndex: 'amountCent',
            title: '价格',
            search: false,
            render: (text: number) => ToolsUtil.moneyCentFormat(text)
          },
          { dataIndex: 'days', title: '有效天数', search: false },
          {
            dataIndex: 'isEnabled',
            title: '是否启用',
            search: true,
            valueType: 'select',
            valueEnum: {
              true: { text: '是', status: 'Success' },
              false: { text: '否', status: 'Default' },
            },
            render: (_, record) => (
              <Switch checked={record.isEnabled} onChange={async (checked) => {
                await apiVipFeeTypeUpdate({ id: record.id, isEnabled: checked });
                tableRef.current?.reload();
              }} />
            )
          },
          {
            dataIndex: 'isPublic',
            title: '是否公开',
            search: true,
            valueType: 'select',
            valueEnum: {
              true: { text: '是', status: 'Success' },
              false: { text: '否', status: 'Default' },
            },
            render: (_, record) => (
              <Switch checked={record.isPublic} onChange={async (checked) => {
                await apiVipFeeTypeUpdate({ id: record.id, isPublic: checked });
                tableRef.current?.reload();
              }}
              />
            )
          },
          {
            dataIndex: 'action',
            title: '操作',
            search: false,
            width: 180,
            render: (_, record) => (
              <Space>
                <a onClick={() => navigate(`/vipFeeTypeDetail?id=${record.id}`)}>查看</a>
                <a onClick={() => showEditModal(record)}>编辑</a>
              </Space>
            )
          },
        ]}
        request={async (params) => {
          const res = await apiVipFeeTypePageList({
            pageIndex: params.current - 1,
            pageSize: params.pageSize,
            id: params.id,
            name: params.name,
            isEnabled: params.isEnabled === 'true' ? true : (params.isEnabled === 'false' ? false : undefined),
            isPublic: params.isPublic === 'true' ? true : (params.isPublic === 'false' ? false : undefined),
          });
          return {
            data: res.rows,
            total: res.totalRowCount,
            success: true,
          };
        }}
      />
      {formModalHolder}
    </div>
  );
}

export default VipFeeTypeList;
