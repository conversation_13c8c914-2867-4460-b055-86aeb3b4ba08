import { UploadOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { HttpUtils } from "@/utils/http";
import { Upload } from "@formily/antd-v5";
import { Image, UploadFile } from "antd";
import {
  connect,
  mapReadPretty,
} from '@formily/react'
import { useMemo, useState } from "react";
import FileUtil from "@/utils/file";

type TFileUploadProps = {
  maxCount?: number;
  listType: 'text' | 'picture' | 'picture-card' | 'picture-circle';
  value: string[];
  keepName?: boolean;
  saveMedia?: boolean;
}


const FileUpload = (props: TFileUploadProps) => {
  const [previewData, setPreviewData] = useState<{
    url: 'image' | 'video';
    type: string;
  }>();
  const [previewOpen, setPreviewOpen] = useState(false);

  const actionUrl = useMemo(() => {
    const keepName = props?.keepName ? '1' : ''
    const saveMedia = props?.saveMedia ? '1' : ''
    return `${HttpUtils.API_SITE}/upload?keepName=${keepName}&saveMedia=${saveMedia}`
  }, [props.keepName, props.saveMedia])

  const handlePreview = async (file: UploadFile) => {
    if (file.response?.data?.url) {
      const ext = (file.response.data.url.split('.').pop()).toLowerCase()
      const type = {
        jpg: 'image',
        jpeg: 'image',
        png: 'image',
        gif: 'image',
        webp: 'image',
        '3gp': 'video',
        mpg: 'video',
        mpeg: 'video',
        mp4: 'video',
        m4v: 'video',
        m4p: 'video',
        ogv: 'video',
        ogg: 'video',
        mov: 'video',
        webm: 'video',
      }[ext]
      if (!type) return
      setPreviewData({
        type,
        url: file.response.data.url
      })
      setPreviewOpen(true)
    }
  };
  return (
    <>
      <Upload
        listType=""
        {...props}
        action={actionUrl}
        method="POST"
        name="file"
        beforeUpload={FileUtil.beforeUpload}
        headers={{ Authorization: `Bearer ${HttpUtils.TOKEN}` }}
        onPreview={handlePreview}
      >
        {props.maxCount && props.value?.length >= props.maxCount ? null : props.listType === "picture-card" ? (
          <UploadOutlined style={{ fontSize: 20 }} />
        ) : (
          <Button icon={<UploadOutlined />}>上传文件</Button>
        )}

      </Upload>
      {previewData && previewData.type === 'image' ? (
        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewData(undefined),
            destroyOnClose: true,
          }}
          src={previewData.url}
        />
      ) : null}
      {previewData && previewData.type === 'video' ? (

        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewData(undefined),
            destroyOnClose: true,
            imageRender: () => (
              <video
                muted
                // width="100%"
                controls
                src={previewData.url}
              />
            ),
            toolbarRender: () => null,
          }}
        // src={previewImage}
        />
      ) : null}
    </>

  );
};

export default connect(
  FileUpload,
  mapReadPretty(() => null),
);
