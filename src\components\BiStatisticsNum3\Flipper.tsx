

import { useEffect, useRef, useState } from "react";
import { useAsyncEffect, useCreation } from "ahooks";
import { ToolsUtil } from "@/utils/tools";
import './Flipper.less';

export interface TFlipperProps {
    num: number;
    size?: number;
    bgColor?: string;
    textColor?: string;

}

export interface TFlipperRef {
    setFront: (front: number) => void;
    setBack: (front: number) => void;
    flipDown: (front: number, back: number) => void;
    flipUp: (front: number, back: number) => void;
}
const Flipper = (props: TFlipperProps) => {
    const { num, size = 60, textColor = '#c9e8f2', bgColor = '#193E36' } = props;

    const scale = useCreation(() => {
        return size / 60;
    }, [size]);

    const [isFlipping, setIsFlipping] = useState(false);
    const [frontNum, setFrontNum] = useState(props.num);
    const [backNum, setBackNum] = useState(props.num);

    const numRef = useRef(num);
    const frontNumRef = useRef(frontNum);

    useEffect(() => {
        numRef.current = num;
    }, [num])

    useEffect(() => {
        const timer = setInterval(async () => {
            if (numRef.current !== frontNumRef.current) {
                const cardNum = (frontNumRef.current + 1) % 10;
                frontNumRef.current = cardNum;
                setFrontNum(cardNum);
                setBackNum(cardNum);
                setIsFlipping(true);
                setTimeout(() => {
                    setIsFlipping(false);
                }, 300);
            } else {
                setIsFlipping(false);
            }
        }, 330)
        return () => {
            clearInterval(timer);
        }
    }, [])

    return <div style={{ width: size, height: 100 * scale, '--flipper-bg-color': bgColor, '--flipper-text-color': textColor } as any}>
        <div className={`Flipper down ${isFlipping ? 'go' : ''}`} style={{ transform: `scale(${scale})`, transformOrigin: '0% 0%' }}>
            <div className="half"></div>
            <div className='digital front' data-num={frontNum}></div>
            <div className='digital back' data-num={backNum}></div>
        </div>
    </div>;
}
export default Flipper;