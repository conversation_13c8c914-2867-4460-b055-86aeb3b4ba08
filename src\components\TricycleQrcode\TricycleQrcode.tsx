import { useCreation, useMemoizedFn } from "ahooks";
import { Modal } from "antd"
import { useState } from "react";
import { ToolsUtil } from "@/utils/tools";

export interface TTricycleQrcodeProps {
  tricycle: TTricycle
}

function TricycleQrcode(props: TTricycleQrcodeProps) {
  const [modalOpen, setModalOpen] = useState(false);
  const qrCodeSvg = useCreation(() => ToolsUtil.tricycleQrcodeSvg(props.tricycle), [props.tricycle])

  const download = useMemoizedFn(async () => {
    const numCode = props.tricycle.number || props.tricycle.numCode
    const jpgBlob = await ToolsUtil.svgToJpgBlog(qrCodeSvg)
    ToolsUtil.downloadBlob(jpgBlob, `${numCode}.jpg`)
  })

  return (
    <div className="inline-block">
      <a onClick={() => setModalOpen(true)}>二维码</a>
      <Modal title="二维码" open={modalOpen} onOk={download} okText="下载" onCancel={() => setModalOpen(false)} cancelText="关闭">
        <div className="flex justify-center">
          <img src={`data:image/svg+xml;utf8,${encodeURIComponent(qrCodeSvg)}`} style={{width: '512px'}} alt="" />
        </div>
      </Modal>
    </div>
  )
}

export default TricycleQrcode
