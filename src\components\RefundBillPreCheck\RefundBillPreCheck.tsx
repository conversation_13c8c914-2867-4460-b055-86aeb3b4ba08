import { apiGetInvoiceByBillId } from "@/apis/apis.api";
import { AgreeDicts } from "@/utils/agreeDict";
import { useRequest } from "ahooks";
import clsx from "clsx";
import { useMemo } from "react";

export interface TRefundBillPreCheckProps {
  billId: string;
}

function RefundBillPreCheck(props: TRefundBillPreCheckProps) {
  const { loading, data, error } = useRequest(async () => {
    return await apiGetInvoiceByBillId(props.billId)
  }, {
    refreshDeps: [props.billId],
  })

  const status = useMemo(() => {
    if (loading) return 'loading';
    if (error) return 'error';
    if (data) return 'warning';
    return 'success';
  }, [loading, error, data])


  return (
    <div className={clsx({
      'RefundBillPreCheck py-2 px-4 rounded-md': true,
      'bg-red-100 text-red-700': status === 'warning',
      'bg-blue-100 text-blue-700': status === 'loading',
      'bg-orange-100 text-orange-700': status === 'error',
      'bg-green-100 text-green-700': status === 'success',
    })}>
      {status === 'loading' && (
        <div>正在查询发票开具情况...</div>
      )}
      {status === 'error' && (
        <div>查询发票开具情况失败</div>
      )}
      {status === 'warning' && (
        <div>此条缴费记录用户已开发票({AgreeDicts.Invoice_status.valueLabelMap[data.status]})，请与用户核实是否需要退款，并且退款前确认发票是否可以红冲</div>
      )}
      {status === 'success' && (
        <div>此条缴费记录用户未开发票</div>
      )}
    </div>
  )
}

export default RefundBillPreCheck
