import { ActionType, ProTable } from "@ant-design/pro-components";
import { App, Modal, Space } from "antd";
import { forwardRef, useImperativeHandle, useRef, useState } from "react";
import { downloadBatchQrcodes } from "./utils";
import { ToolsUtil } from "@/utils/tools";
import { apiTricycleBatchGetById, apiTricycleBatchPageList } from "@/apis/apis.api";
import { AgreeDicts } from "@/utils/agreeDict";
import { useMemoizedFn } from "ahooks";

export type TTricycleBatchListRef = {
    show: () => void
    reload: () => void
}

const TriclcyeBatchList = forwardRef<TTricycleBatchListRef, any>((_props, ref) => {

    const [open, setOpen] = useState(false)
    const tableRef = useRef<ActionType>();
    const { modal } = App.useApp()

    useImperativeHandle(ref, () => ({
        show: () => {
            tableRef.current?.reload()
            setOpen(true)
        },
        reload: () => {
            tableRef.current?.reload()
        }
    }))

    const showNumCode = useMemoizedFn(async (recordId: string) => {
        const res = await apiTricycleBatchGetById(recordId) 
        modal.info({
            title: '三轮车编号',
            content: res.tricycles.map(item => item.numCode).join('\n'),
        })
    })
    return <Modal open={open} onCancel={() => setOpen(false)} width={1200}>
        <ProTable<TTricycleBatch>
            actionRef={tableRef}
            headerTitle="三轮车批量记录"
            rowKey="id"
            search={false}
            pagination={{
                pageSize: 10,
            }}
            columns={[
                { title: 'ID', dataIndex: 'id' },
                { title: '批次名称', dataIndex: 'name' },
                { title: '描述', dataIndex: 'description' },
                { title: '创建时间', dataIndex: 'createdAt' },
                { title: '创建个数', dataIndex: 'count' },
                { title: '类型', dataIndex: 'type', valueEnum: AgreeDicts.TricycleBatch_type.valueLabelMap },
                {
                    title: '操作',
                    valueType: 'option',
                    key: 'option',
                    render: (_text, record) => {
                        return (<Space>
                            {[
                                AgreeDicts.TricycleBatch_type.labelValueMap['创建'],
                                AgreeDicts.TricycleBatch_type.labelValueMap['制作'],
                            ].includes(record.type.toString()) ? <a onClick={() => downloadBatchQrcodes(record)}>下载</a> : null}
                            {/* <a onClick={() => showNumCode(record.id)}>查看编号</a> */}
                        </Space>)
                    }
                }
            ]}
            request={async (params, sorter) => {

                const filter: TFilterItem<TTricycleBatch>[] = []

                const sort = ToolsUtil.tableSorterToPageListReqSort<TTricycleBatch>(sorter)

                const res = await apiTricycleBatchPageList({
                    pageNum: params.current,
                    pageSize: params.pageSize,
                    filter,
                    sort,
                })
                return {
                    total: res.total,
                    data: res.list,
                    success: true,
                }
            }}
        />
    </Modal>;
});

export default TriclcyeBatchList;