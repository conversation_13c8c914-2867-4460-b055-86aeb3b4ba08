import { useEffect } from 'react';
import { useEcharts } from '@/hooks/useEcharts';
import { useCreation, useRequest } from 'ahooks';
import { apiBiFlowerPrice } from '@/apis/apis.api';

const Bi01Trading7d = () => {
  const { chart, setContainerRef } = useEcharts();

  const { data: flowerPriceData } = useRequest(async () => {
    const res = await apiBiFlowerPrice();
    // console.log('apiBiFlowerPrice', res)
    return res
  })

  // const { data: flowerPriceData } = useRequest(async () => {
  //   const response = await fetch('/jsons/flowerPrice.json');
  //   return response.json();
  // })

  const data = useCreation(() => {
    if (!flowerPriceData || !flowerPriceData.length) return null;
    const groupByDay = flowerPriceData.reduce((pre, cur) => {
      if(!pre[cur.day]) {
        pre[cur.day] = {}
      }
      pre[cur.day][cur.classify] = cur.avg
      return pre
    }, {})
    const maxDay = Object.keys(groupByDay).sort((a, b) => b.localeCompare(a))[0]
    const classifyList = Object.keys(groupByDay[maxDay]).filter(item => {
      return Object.keys(groupByDay).every(day => groupByDay[day][item])
    }).sort((a, b) => groupByDay[maxDay][b] - groupByDay[maxDay][a]).slice(0, 10)
    const result: {
      name: string;
      list: {
        date: string;
        total: number;
      }[]
    }[] = [];

    for (let i = 0; i < flowerPriceData.length; i++) {
      const ai = classifyList.indexOf(flowerPriceData[i].classify)
      if(ai === -1) continue;
      if(!result[ai]) {
        result[ai] = {
          name: classifyList[ai],
          list: []
        }
      }
      result[ai].list.push({
        date: flowerPriceData[i].day.slice(4,6) + '-' + flowerPriceData[i].day.slice(6,8),
        total: Number(flowerPriceData[i].avg.toFixed(2)),
      })
    }

    return result
  }, [flowerPriceData])

  useEffect(() => {
    if (!data || !chart) return;

    const colors = [
      '#FF4D4F', // 鲜红
      '#13C2C2', // 青色
      '#FAAD14', // 金黄
      '#722ED1', // 紫色
      '#52C41A', // 绿色
      '#1890FF', // 蓝色
      '#EB2F96', // 粉红
      '#FA8C16', // 橙色
      '#2F54EB', // 深蓝
      '#F5222D', // 红色
    ];
    const areas = data.map(item => item.name);
    const dates = data[0].list.map(item => item.date);

    const option = {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.6)',
        borderColor: 'rgba(255,255,255,0.2)',
        textStyle: {
          color: '#fff'
        },
        formatter: (params: any[]) => {
          let result = params[0].axisValue + '<br/>';
          params.forEach(item => {
            result += `${item.marker} ${item.seriesName}: ${item.value}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: areas,
        left: 100,
        right: 100,
        textStyle: {
          color: '#E6EAF2'
        },
      },
      grid: {
        left: 20,
        right: 50,
        bottom: 0,
        top: 40,
        containLabel: true
      },
      xAxis: {
        name: '日期',
        nameTextStyle: {
          color: '#02CABB',
          align: 'left',
        },
        type: 'category',
        boundaryGap: false,
        data: dates,
        axisLine: {
          lineStyle: {
            color: '#37435A'
          }
        },
        axisLabel: {
          color: '#E6EAF2'
        }
      },
      yAxis: {
        name: '价格(元/单位)',
        nameTextStyle: {
          color: '#02CABB',
        },
        type: 'value',
        splitLine: {
          lineStyle: {
            color: '#37435A'
          }
        },
        axisLabel: {
          color: '#E6EAF2',
          formatter: (value: number) => {
            return value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value;
          }
        }
      },
      series: areas.map((area, ai) => ({
        name: area,
        type: 'line',
        smooth: true,
        symbol: 'none',
        itemStyle: {
          color: colors[ai % colors.length]
        },
        data: data[ai].list.map(item => item.total),
        lineStyle: {
          width: 2,
          color: colors[ai % colors.length],
        },
      }))
    };
    chart.clear();
    chart.setOption(option);
  }, [chart, data]);

  useEffect(() => {

    if(!chart || !data) return;
    let index = 0;
    const run = () => {
      chart.dispatchAction({
        type: 'showTip',
        seriesIndex: 0,
        dataIndex: index
      })
      index ++
      if(index > 6) {
        index = 0;
      }
    }
    run();
    const timer = setInterval(run, 10 * 1000)

    return () => {
      clearInterval(timer);
    }
    

  }, [data, chart])

  return <div ref={setContainerRef} className="w-full h-full" />;
};

export default Bi01Trading7d;
