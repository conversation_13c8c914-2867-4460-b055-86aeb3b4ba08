import { FormLayout } from "@formily/antd-v5";
import { FormProvider } from "@formily/react";
import { SchemaField, useForm } from "@/components/Formily";
import { useModel } from 'foca';
import { settingModel } from '@/stores/settingModel';
import { useEffect } from "react";

const ShopSetting = () => {
  const form = useForm({});
  const settingModelState = useModel(settingModel);

  useEffect(() => {
    form.setInitialValues(settingModelState.Shop)
    form.setValues(settingModelState.Shop)
  }, [settingModelState.Shop])

  return (
    <FormProvider form={form}>
      <FormLayout layout="vertical" bordered>
        <SchemaField>
          <SchemaField.Number
            name="shop_autoHot_favouriteCount"
            title="热点条件N1(关注数)"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '自动成为热点: 关注数 >= N1 且 浏览数 >= N2 且 通话数 >= N3',
              parentKey: 'Shop',
            }}
            x-validator={[
              { min: 0, message: '最小为0' }
            ]}
            default={0}
          />
          <SchemaField.Number
            name="shop_autoHot_viewCount"
            title="热点条件N2(浏览数)"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '自动成为热点: 关注数 >= N1 且 浏览数 >= N2 且 通话数 >= N3',
              parentKey: 'Shop',
            }}
            x-validator={[
              { min: 0, message: '最小为0' }
            ]}
            default={0}
          />
          <SchemaField.Number
            name="shop_autoHot_brokerCalledCount"
            title="热点条件N3(通话数)"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '自动成为热点: 关注数 >= N1 且 浏览数 >= N2 且 通话数 >= N3',
              parentKey: 'Shop',
            }}
            x-validator={[
              { min: 0, message: '最小为0' }
            ]}
            default={0}
          />
          <SchemaField.Number
            name="shop_overallRank_favouriteCount"
            title="排名系数W1(关注数)"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '排名得分: 关注数 * W1 + 浏览数 * W2 + 通话数 * W3 + 推荐值 * W4',
              parentKey: 'Shop',
            }}
            x-validator={[
              { min: 0, message: '最小为0' }
            ]}
            default={0}
          />
          <SchemaField.Number
            name="shop_overallRank_viewCount"
            title="排名系数W2(浏览数)"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '排名得分: 关注数 * W1 + 浏览数 * W2 + 通话数 * W3 + 推荐值 * W4',
              parentKey: 'Shop',
            }}
            x-validator={[
              { min: 0, message: '最小为0' }
            ]}
            default={0}
          />
          <SchemaField.Number
            name="shop_overallRank_brokerCalledCount"
            title="排名系数W3(通话数)"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '排名得分: 关注数 * W1 + 浏览数 * W2 + 通话数 * W3 + 推荐值 * W4',
              parentKey: 'Shop',
            }}
            x-validator={[
              { min: 0, message: '最小为0' }
            ]}
            default={0}
          />
          <SchemaField.Number
            name="shop_overallRank_recommend"
            title="排名系数W4(推荐值)"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '排名得分: 关注数 * W1 + 浏览数 * W2 + 通话数 * W3 + 推荐值 * W4',
              parentKey: 'Shop',
            }}
            x-validator={[
              { min: 0, message: '最小为0' }
            ]}
            default={0}
          />
        </SchemaField>
      </FormLayout>
    </FormProvider>
  );
}
export default ShopSetting;