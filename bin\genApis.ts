import fs from 'fs';
import path from 'path';
import http from 'http';
import https from 'https';
import yaml from 'js-yaml';
import { URL } from 'url';


interface OpenAPISchema {
  openapi: string;
  info: any;
  paths: Record<string, any>;
  components?: {
    schemas?: Record<string, any>;
  };
}

interface SchemaProperty {
  type?: string;
  format?: string;
  nullable?: boolean;
  $ref?: string;
  description?: string;
  summary?: string;
  items?: {
    $ref?: string;
    type?: string;
  };
  properties?: Record<string, SchemaProperty>;
}

const API_PREFIX = '/api/admin'

// 可以通过命令行参数传入下载URL，否则使用默认值
const downloadUrl = process.argv[2] || 'http://localhost:8080/openapi.yml';


// 工具函数：OpenAPI类型转TS类型（可复用）
function openApiTypeToTsType(type: string, format?: string): string {
  switch (type) {
    case 'integer':
    case 'number':
      return 'number';
    case 'string':
      return format === 'date-time' ? 'string' : 'string';
    case 'boolean':
      return 'boolean';
    case 'object':
      return 'Record<string, any>';
    default:
      return type || 'any';
  }
}



// 下载YAML文件
async function downloadYaml(url: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const requestLib = parsedUrl.protocol === 'https:' ? https : http;

    requestLib.get(url, (res) => {
      if (res.statusCode !== 200) {
        reject(new Error(`请求失败，状态码: ${res.statusCode}`));
        return;
      }

      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        resolve(data);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// 将引用路径转换为类型名称
function refToTypeName(ref: string): string {
  if (!ref) return 'any';

  // 从引用路径中提取类型名称
  let typeName = ref.split('/').pop() || '';
  typeName = typeName.replace(/^Dynamic_/, '');
  typeName = typeName.replace(/Dto$/, '');
  const match = typeName.match(/^Page_Dynamic_(.+)$/);
  if (match) {
    typeName = typeName.replace(/^Page_Dynamic_(.+)$/, '$1Page');
  }
  if (typeName.startsWith('T') && typeName[1] === typeName[1].toUpperCase()) {
    return typeName;
  }
  return 'T' + typeName[0].toUpperCase() + typeName.slice(1);
}

// 生成TypeScript类型定义
function generateTypeDefinition(schema: any, name: string): string {
  if (!schema) return '';

  // 将Dynamic_前缀替换为T，并处理Page_Dynamic_xxx为TxxxPage
  const typeName = refToTypeName(name);

  if (schema.type === 'object') {
    let typeDefinition = '';

    // 添加类型注释
    if (schema.description) {
      typeDefinition += `/**\n * ${schema.description}\n */\n`;
    }

    typeDefinition += `interface ${typeName} {\n`;

    if (schema.properties) {
      for (const [propName, propSchema] of Object.entries<SchemaProperty>(schema.properties)) {
        let propType = 'any';

        if (propSchema.$ref) {
          propType = refToTypeName(propSchema.$ref);
        } else if (propSchema.type === 'array' && propSchema.items) {
          if (propSchema.items.$ref) {
            propType = `${refToTypeName(propSchema.items.$ref)}[]`;
          } else {
            propType = `${propSchema.items.type || 'any'}[]`;
          }
        } else if (propSchema.type) {
          // 将OpenAPI类型转换为TypeScript类型（复用工具函数）
          propType = openApiTypeToTsType(propSchema.type, propSchema.format);
        }

        // 处理可空属性
        const isNullable = propSchema.nullable === true;

        // 添加属性注释
        if (propSchema.description) {
          typeDefinition += `  /** ${propSchema.description} */\n`;
        }

        typeDefinition += `  ${propName}${isNullable ? '?' : ''}: ${propType};\n`;
      }
    }

    typeDefinition += '}\n\n';
    return typeDefinition;
  }

  return '';
}

// 生成API接口
function generateApiInterface(path: string, method: string, operation: any): string {
  if (!operation || !operation.operationId) return '';

  const operationId = operation.operationId.replace(/_\d+$/, '');
  const apiName = `api${operationId.charAt(0).toUpperCase() + operationId.slice(1)}`;

  // 提取路径参数
  const pathParams: string[] = [];
  const pathRegex = /{([^}]+)}/g;
  let match: RegExpExecArray | null;
  while ((match = pathRegex.exec(path)) !== null) {
    pathParams.push(match[1]);
  }

  // 提取请求参数
  const parameters = operation.parameters || [];
  const queryParams: string[] = [];
  const paramTypeMap: Record<string, string> = {};

  // 生成参数类型字符串（新增工具函数）
  function genParamType(param: any): string {
    if (param.schema) {
      if (param.schema.$ref) {
        return refToTypeName(param.schema.$ref);
      }
      if (param.schema.type === 'array' && param.schema.items) {
        if (param.schema.items.$ref) {
          return `${refToTypeName(param.schema.items.$ref)}[]`;
        } else {
          return `${openApiTypeToTsType(param.schema.items.type)}[]`;
        }
      }
      return openApiTypeToTsType(param.schema.type, param.schema.format);
    }
    return 'any';
  }

  for (const param of parameters) {
    if (param.in === 'query') {
      queryParams.push(param.name);
      paramTypeMap[param.name] = genParamType(param);
    } else if (param.in === 'path') {
      paramTypeMap[param.name] = genParamType(param);
    }
  }

  // 提取响应类型
  let responseType = 'any';
  if (operation.responses && operation.responses['200'] && operation.responses['200'].content) {
    const content = operation.responses['200'].content;
    if (content['application/json'] && content['application/json'].schema) {
      const schema = content['application/json'].schema;
      if (schema.$ref) {
        responseType = refToTypeName(schema.$ref);
      } else if (schema.type === 'array' && schema.items) {
        if (schema.items.$ref) {
          responseType = `${refToTypeName(schema.items.$ref)}[]`;
        } else {
          responseType = `${openApiTypeToTsType(schema.items.type)}[]`;
        }
      } else if (schema.type) {
        responseType = openApiTypeToTsType(schema.type, schema.format);
      }
    }
  }

  // 生成函数参数
  const functionParams: string[] = [];
  const paramDescriptions: Record<string, string> = {};

  // 路径参数
  for (const param of parameters) {
    if (param.in === 'path') {
      const tsType = paramTypeMap[param.name] || 'any';
      functionParams.push(`${param.name}: ${tsType}`);
      if (param.description) {
        paramDescriptions[param.name] = param.description;
      }
    }
  }

  // 查询参数
  if (queryParams.length > 0) {
    const queryType = queryParams.map(name => `${name}${parameters.find(p => p.name===name)?.required ? '' : '?'}: ${paramTypeMap[name]}`).join('; ');
    functionParams.push(`query?: { ${queryType} }`);
    queryParams.forEach(name => {
      const param = parameters.find(p => p.name === name);
      if (param && param.description) {
        paramDescriptions[name] = param.description;
      }
    });
  }

  // 添加请求体参数
  if (['post', 'put', 'patch'].includes(method.toLowerCase()) && operation.requestBody) {
    const requestBody = operation.requestBody;
    if (requestBody.content && requestBody.content['application/json']) {
      const schema = requestBody.content['application/json'].schema;
      let requestType = 'any';

      if (schema.$ref) {
        requestType = refToTypeName(schema.$ref);
      }

      const isRequired = requestBody.required === true;
      functionParams.push(`data${isRequired ? '' : '?'}: ${requestType}`);

      if (requestBody.description) {
        paramDescriptions['data'] = requestBody.description;
      }
    }
  }

  // 生成API函数注释
  let apiFunction = '';

  // 添加函数注释
  apiFunction += '/**\n';
  if (operation.summary) {
    apiFunction += ` * ${operation.summary}\n`;
  }
  if (operation.description) {
    apiFunction += ` * ${operation.description}\n`;
  }

  // 添加参数注释
  for (const [paramName, description] of Object.entries(paramDescriptions)) {
    apiFunction += ` * @param ${paramName} ${description}\n`;
  }

  // 添加返回值注释
  apiFunction += ` * @returns ${responseType} 对象\n`;
  apiFunction += ' */\n';

  // 生成API函数
  apiFunction += `export function ${apiName}(${functionParams.join(', ')}) {\n`;

  // 替换路径中的参数
  let apiPath = path;
  pathParams.forEach(param => {
    apiPath = apiPath.replace(`{${param}}`, `\${${param}}`);
  });

  // 构建请求选项
  apiFunction += `  return HttpUtils.request<${responseType}>({\n`;
  apiFunction += `    url: \`${apiPath}\`,\n`;

  if (method.toLowerCase() !== 'get') {
    apiFunction += `    method: '${method.toUpperCase()}',\n`;
  }

  // 只有当请求体参数存在且在函数参数中定义了data时才添加data参数
  if (['post', 'put', 'patch'].includes(method.toLowerCase()) && 
      operation.requestBody && 
      functionParams.some(param => param.startsWith('data'))) {
    apiFunction += `    data,\n`;
  }

  if (queryParams.length > 0) {
    apiFunction += `    query,\n`;
  }

  apiFunction += `  })\n`;
  apiFunction += `}\n\n`;

  return apiFunction;
}

async function main() {
  try {
    let yamlContent = '';

    // 检查是否有命令行参数提供的YAML内容文件
    if (process.argv.length > 2 && fs.existsSync(process.argv[2])) {
      console.log(`正在从文件 ${process.argv[2]} 加载OpenAPI文档...`);
      yamlContent = fs.readFileSync(process.argv[2], 'utf-8');
    } else if (process.argv.length > 2 && process.argv[2].startsWith('http')) {
      // 如果参数是URL，则从URL下载
      console.log(`正在从 ${process.argv[2]} 下载OpenAPI文档...`);
      yamlContent = await downloadYaml(process.argv[2]);
    } else {
      // 否则使用默认URL
      console.log(`正在从 ${downloadUrl} 下载OpenAPI文档...`);
      yamlContent = await downloadYaml(downloadUrl);
    }

    console.log('解析YAML文档...');
    const openApiDoc = yaml.load(yamlContent) as OpenAPISchema;

    if (!openApiDoc || !openApiDoc.paths) {
      throw new Error('无效的OpenAPI文档');
    }

    // 生成类型定义
    console.log('生成类型定义...');
    let typeDefinitions = '';

    if (openApiDoc.components && openApiDoc.components.schemas) {
      for (const [schemaName, schema] of Object.entries(openApiDoc.components.schemas)) {
        typeDefinitions += generateTypeDefinition(schema, schemaName);
      }
    }

    // 确保目录存在
    const typesDir = path.resolve('./src/types');
    if (!fs.existsSync(typesDir)) {
      fs.mkdirSync(typesDir, { recursive: true });
    }

    // 写入类型定义文件
    fs.writeFileSync(path.resolve('./src/types/model.d.ts'), typeDefinitions);
    console.log('类型定义已生成到 types/model.d.ts');

    // 生成API接口
    console.log('生成API接口...');
    let apiInterfaces = `import { HttpUtils } from "@/utils/http";\n\n`;

    for (const [pathUrl, pathItem] of Object.entries(openApiDoc.paths)) {
      // 只处理以 API_PREFIX 开头的路径
      if (pathUrl.startsWith(API_PREFIX)) {
        // 删除 API_PREFIX 前缀
        const processedPath = pathUrl.substring(API_PREFIX.length);
        for (const [method, operation] of Object.entries(pathItem)) {
          if (['get', 'post', 'put', 'delete', 'patch'].includes(method)) {
            apiInterfaces += generateApiInterface(processedPath, method, operation);
          }
        }
      }
    }

    // 确保目录存在
    const apisDir = path.resolve('./src/apis');
    if (!fs.existsSync(apisDir)) {
      fs.mkdirSync(apisDir, { recursive: true });
    }

    // 写入API接口文件
    fs.writeFileSync(path.resolve('./src/apis/apis.ts'), apiInterfaces);
    console.log('API接口已生成到 src/apis/apis.ts');

    console.log('生成完成！');
  } catch (error) {
    console.error('生成失败:', error);
    process.exit(1);
  }
}

// 执行主函数
main();
