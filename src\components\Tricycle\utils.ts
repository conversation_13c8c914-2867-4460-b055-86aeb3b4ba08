import { apiTricycleBatchGetById } from "@/apis/apis.api";
import { ToolsUtil } from "@/utils/tools";
import {
    BlobWriter,
    BlobReader,
    TextReader,
    ZipWriter,
} from '@zip.js/zip.js'

export const downloadBatchQrcodes = async (record: TTricycleBatch) => {
    const res = await apiTricycleBatchGetById(record.id)
    const zipName = `三轮车批量二维码_${res.name}.zip`
    const zipWriter = new ZipWriter(new BlobWriter("application/zip"));
    let numCodesText = ''
    const func = res.tricycles.map(async (tricycle, index) => {
        const svg = ToolsUtil.tricycleQrcodeSvg(tricycle)
        zipWriter.add(`svg/${index + 1}.svg`, new TextReader(svg))

        const jpgBlob = await ToolsUtil.svgToJpgBlog(svg)

        zipWriter.add(`jpg/${index + 1}.jpg`, new BlobReader(jpgBlob), { level: 0 })
        numCodesText += `${index + 1}\n${tricycle.numCode}\n`
    })
    await Promise.all(func)
    zipWriter.add(`code.txt`, new TextReader(numCodesText))
    const zipBlob = await zipWriter.close();
    const zipLocalUrl = URL.createObjectURL(zipBlob);
    const a = document.createElement('a');
    a.href = zipLocalUrl;
    a.download = zipName;
    a.click();
    URL.revokeObjectURL(zipLocalUrl);
}

