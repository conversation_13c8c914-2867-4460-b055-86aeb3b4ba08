import { apiBiPeopleCountInAndOutReport } from "@/apis/apis.api";
import { useEcharts } from "@/hooks/useEcharts";
import { useStepedRequest } from "@/hooks/useStepedRequest";
import { settingModel } from "@/stores/settingModel";
// import { visitorsModel } from "@/stores/visitorsModel";
// import { ToolsUtil } from "@/utils/tools";
import { useCreation, useRequest } from "ahooks"
import dayjs from "dayjs";
import { useModel } from "foca";
// import { useModel } from "foca";
import { useEffect } from "react";

const Bi01VisitorCount24h = () => {
    const settingModelState = useModel(settingModel);
    const peopleCountRate = useCreation(() => settingModelState?.Admin?.peopleCountRate || 1, [settingModelState?.Admin?.peopleCountRate]);
    // refreshDeps: [peopleCountRate]

    // const { visitorsData } = useModel(visitorsModel);
    const apiBiPeopleCountInAndOutReportSteped = useStepedRequest(apiBiPeopleCountInAndOutReport);


    const { data } = useRequest(async () => {
        const start = dayjs().subtract(24, 'hour').format('YYYY-MM-DD HH');
        const end = dayjs().subtract(1, 'hour').format('YYYY-MM-DD HH');
        const res = await apiBiPeopleCountInAndOutReportSteped({
            timeUnit: 'hour',
            start: start,
            end: end
        })
        const list = res.in.reduce((pre, cur) => {
            pre.push(...cur.data)
            return pre
        }, []).reduce((pre, cur, index, arr) => {
            if (index % 2 === 1) {
                pre.push({
                    hour: (arr[index - 1].showTime || '').slice(0, 2) + '-' + (cur.showTime || '').slice(0, 2),
                    inCount: Math.round(((arr[index - 1].remainder || 0) + (cur.remainder || 0)) * peopleCountRate),
                })
            }
            return pre
        }, [])
        return list
    }, {
        pollingInterval: 60 * 60 * 1000,
        refreshDeps: [peopleCountRate],
    })

    const { chart, setContainerRef } = useEcharts({
        theme: 'walden',
    });

    useEffect(() => {
        if (chart && data) {
            const xData = data.map((item) => item.hour);
            const yData = data.map((item) => item.inCount);

            chart.setOption({
                grid: {
                    left: 50,
                    right: 40,
                    bottom: 40,
                    top: 30,

                },
                tooltip: {
                    trigger: 'axis',
                },
                xAxis: {
                    name: '时间\n(时)',
                    nameTextStyle: {
                        color: '#02CABB',
                        align: 'left',
                    },
                    type: 'category',
                    data: xData,
                    axisTick: {
                        show: false,
                        interval: 0,//使x轴文字显示全
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#C9E8F2'
                        }
                    },
                    axisLabel: {
                        rotate: 45,
                        interval: 0,
                    },
                },
                yAxis: {
                    name: '人数(人)',
                    nameTextStyle: {
                        color: '#02CABB',
                        padding: [0, 0, 4, 0]
                    },
                    type: 'value',

                    axisLabel: {
                        formatter: (value, index) => {
                            if (index === yData.length - 1) {
                                return `${value}\n人数(人)`;
                            }
                            return value;
                        }
                    },
                },
                series: [
                    {
                        name: '入场数',
                        type: 'bar',
                        barWidth: 12,
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 1,
                                x2: 0,
                                y2: 0,
                                colorStops: [{
                                    offset: 0, color: '#00D5FF00'
                                }, {
                                    offset: 0.9, color: '#00D5FF'
                                }, {
                                    offset: 1, color: '#fff'
                                }],
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            valueAnimation: true,
                            borderWidth: 1,
                            borderColor: '#68F5FF',
                            backgroundColor: '#68F5FF88',
                            padding: [2, 4],
                            color: "#fff",
                            fontSize: 8,
                            borderRadius: 1,
                        },
                        data: yData,
                    },

                ]
            })
        }
    }, [chart, data])

    return (<div className="h-full w-full" ref={setContainerRef}>
    </div>)
}

export default Bi01VisitorCount24h;