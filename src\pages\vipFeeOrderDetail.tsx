import { apiVipFeeOrderGetById } from "@/apis/apis";
import UserObj from "@/components/UserObj";
import { AgreeDicts } from "@/utils/agreeDict";
import { ToolsUtil } from "@/utils/tools";
import { PageContainer, ProDescriptions } from "@ant-design/pro-components";
import { Card, Spin } from "antd";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

function VipFeeOrderDetail() {
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<TVipFeeOrder | null>(null);
  const params = new URLSearchParams(location.search);
  const id = params.get('id');

  useEffect(() => {
    if (id) {
      fetchData();
    }
  }, [id]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const res = await apiVipFeeOrderGetById({ id: id as string });
      setData(res);
    } catch (error) {
      console.error('获取会员费订单详情失败', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageContainer
      title="会员费订单详情"
      onBack={() => navigate(-1)}
    >
      <Card>
        {loading ? (
          <div style={{ textAlign: 'center', padding: 20 }}>
            <Spin />
          </div>
        ) : data ? (
          <ProDescriptions
            column={2}
            title={false}
            dataSource={data}
            columns={[
              { title: 'ID', dataIndex: 'id' },
              { title: '客户', dataIndex: 'clientUserKeyword', render: (_text, record) => <UserObj userObj={record.clientUser} /> },
              { title: '会员费类型', dataIndex: 'vipFeeTypeName' },
              { title: '会员费天数', dataIndex: 'vipFeeTypeDays' },
              { 
                title: '状态', 
                dataIndex: 'status',
                valueEnum: AgreeDicts.VipFeeOrder_status.valueLabelMap,
              },
              { title: '金额', dataIndex: 'amountCent', render: (text: number) => ToolsUtil.moneyCentFormat(text) },
              { title: '作用开始', dataIndex: 'startTime', render: (text: string) => text ? ToolsUtil.timeFormat(text) : '-' },
              { title: '作用结束', dataIndex: 'endTime', render: (text: string) => text ? ToolsUtil.timeFormat(text) : '-' },
              { title: '创建时间', dataIndex: 'createdAt', render: (text: string) => ToolsUtil.timeFormat(text) },
            ]}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: 20 }}>未找到会员费订单信息</div>
        )}
      </Card>
    </PageContainer>
  );
}

export default VipFeeOrderDetail;
