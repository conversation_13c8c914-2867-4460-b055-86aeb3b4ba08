import { apiClientUserGetById, apiPointsRecordPageList } from "@/apis/apis";
import { PageContainer, ProDescriptions, ProTable, ActionType, ProCard } from "@ant-design/pro-components";
import { Card, Spin, Button, Space, Tag, Tabs } from "antd";
import { useEffect, useState, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { usePointsAdjustmentFormModal } from "@/hooks/usePointsAdjustmentFormModal";
import { AgreeDicts } from "@/utils/agreeDict";

function ClientUserDetail() {
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<TClientUser | null>(null);
  const params = new URLSearchParams(location.search);
  const id = params.get('id');
  const pointsTableRef = useRef<ActionType>();

  const { showAdjustModal, formModalHolder } = usePointsAdjustmentFormModal(() => {
    fetchData();
    pointsTableRef.current?.reload();
  });

  useEffect(() => {
    if (id) {
      fetchData();
    }
  }, [id]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const res = await apiClientUserGetById({ id: id as string });
      setData(res);
    } catch (error) {
      console.error('获取客户详情失败', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageContainer
      title="客户详情"
      onBack={() => navigate(-1)}
    >
      {loading ? (
        <Card>
          <div style={{ textAlign: 'center', padding: 20 }}>
            <Spin />
          </div>
        </Card>
      ) : data ? (
        <div className="flex flex-col gap-4">
          <ProCard>
            <ProDescriptions
              column={2}
              title={false}
              dataSource={data}
              columns={[
                { title: 'ID', dataIndex: 'id' },
                { title: '昵称', dataIndex: 'nickname' },
                { title: '手机号', dataIndex: 'mobile' },
                { title: '邮箱', dataIndex: 'email' },
                { title: '微信OpenID', dataIndex: 'wxOpenid' },
                {
                  title: 'VIP状态',
                  dataIndex: 'isVip',
                  valueEnum: {
                    true: { text: '是', status: 'Success' },
                    false: { text: '否', status: 'Default' },
                  }
                },
                { title: 'VIP至', dataIndex: 'vipEndAt', valueType: 'dateTime' },
                {
                  title: '当前积分',
                  dataIndex: 'points',
                  render: (text: number) => (
                    <span style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
                      {text}
                    </span>
                  )
                },
                { title: '创建时间', dataIndex: 'createdAt', valueType: 'dateTime' },
              ]}
            />
          </ProCard>
          <ProTable<TClientUserPointsRecord>
            headerTitle="积分变化记录"
            actionRef={pointsTableRef}
            rowKey="id"
            search={{
              filterType: 'light',
              // defaultCollapsed: false,
            }}
            pagination={{
              pageSize: 20,
              showQuickJumper: true,
            }}
            toolBarRender={() => [
              <Button
                key="adjust"
                type="primary"
                onClick={() => data && showAdjustModal(data)}
              >
                手动调整积分
              </Button>,
            ]}
            columns={[
              { dataIndex: 'id', title: 'ID', search: true, width: 180 },
              {
                dataIndex: 'recordType',
                title: '记录类型',
                valueType: 'select',
                valueEnum: AgreeDicts.ClientUserPointsRecord_recordType.valueLabelMap,
                render: (_, record) => {
                  const typeData = AgreeDicts.ClientUserPointsRecord_recordType.valueDataMap[record.recordType];
                  return (
                    <Tag color={typeData?.data?.color || 'default'}>
                      {typeData?.label || record.recordType}
                    </Tag>
                  );
                }
              },
              {
                dataIndex: 'pointsChange',
                title: '积分变化',
                search: false,
                render: (text: number) => (
                  <span style={{ color: text > 0 ? '#52c41a' : text < 0 ? '#ff4d4f' : '#666' }}>
                    {text > 0 ? '+' : ''}{text}
                  </span>
                )
              },
              { dataIndex: 'description', title: '描述', search: false },
              {
                dataIndex: 'relatedBillId',
                title: '关联订单ID',
                search: true,
                render: (text: string) => text || '-'
              },
              {
                dataIndex: 'createdAt',
                title: '创建时间',
                valueType: 'dateTimeRange',
                search: {
                  transform: (value) => ({
                    createdAtStart: value[0],
                    createdAtEnd: value[1],
                  }),
                },
                render: (_, record) => record.createdAt
              },
            ]}
            request={async (params) => {
              if (!id) return { data: [], total: 0, success: false };

              const res = await apiPointsRecordPageList({
                pageIndex: (params.current || 1) - 1,
                pageSize: params.pageSize || 20,
                clientUserId: id,
                id: params.id,
                recordType: params.recordType,
                relatedBillId: params.relatedBillId,
                createdAtStart: params.createdAtStart,
                createdAtEnd: params.createdAtEnd,
              });
              return {
                data: res.rows,
                total: res.totalRowCount,
                success: true,
              };
            }}
          />
        </div>

      ) : (
        <Card>
          <div style={{ textAlign: 'center', padding: 20 }}>未找到客户信息</div>
        </Card>
      )}
      {formModalHolder}
    </PageContainer>
  );
}

export default ClientUserDetail;
