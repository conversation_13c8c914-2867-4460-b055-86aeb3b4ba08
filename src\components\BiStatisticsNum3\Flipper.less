

.Flipper {
    display: inline-block;
    position: relative;
    width: 60px;
    height: 100px;
    line-height: 100px;
    border-radius: 10px;
    background: var(--flipper-bg-color);
    font-size: 66px;
    color: var(--flipper-text-color);
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.5);
    text-align: center;
    font-family: 'Helvetica Neue';
}

.Flipper .half {
    position: absolute;
    z-index: 9;
    width: 100%;
    height: 40px;
    overflow: hidden;
    background: linear-gradient(to bottom, #000 0%, #00000000 50%);
    top: 50%;
    opacity: 0.2;
    border-top: 1px solid #999;
}


.Flipper .digital:before,
.Flipper .digital:after {
    content: attr(data-num);
    position: absolute;
    left: 0;
    right: 0;
    background: var(--flipper-bg-color);
    overflow: hidden;
    box-sizing: border-box;
    font-weight: bold;
}

.Flipper .digital:before {
    top: 0;
    bottom: 50%;
    border-radius: 10px 10px 0 0;
    // border-bottom: solid 1px #666;
}

.Flipper .digital:after {
    top: 50%;
    bottom: 0;
    border-radius: 0 0 10px 10px;
    line-height: 0;
}

/*向下翻*/
.Flipper.down .front:before {
    z-index: 3;
}

.Flipper.down .back:after {
    z-index: 2;
    transform-origin: 50% 0%;
    transform: perspective(160px) rotateX(180deg);
}

.Flipper.down .front:after,
.Flipper.down .back:before {
    z-index: 1;
}

.Flipper.down.go .front:before {
    transform-origin: 50% 100%;
    animation: frontFlipDown 0.6s ease-in-out both;
    box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.3);
    backface-visibility: hidden;
}

.Flipper.down.go .back:after {
    animation: backFlipDown 0.6s ease-in-out both;
}


@keyframes frontFlipDown {
    0% {
        transform: perspective(160px) rotateX(0deg);
    }

    100% {
        transform: perspective(160px) rotateX(-180deg);
    }
}

@keyframes backFlipDown {
    0% {
        transform: perspective(160px) rotateX(180deg);
    }

    100% {
        transform: perspective(160px) rotateX(0deg);
    }
}

