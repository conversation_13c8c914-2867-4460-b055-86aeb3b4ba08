import Flipper from "./Flipper";
import { useCreation } from "ahooks";

export interface TBiStatisticsNum3Props {
  value: number;
  size?: number;
  len?: number;
  label?: string;
  labelSize?: number;
  bgColor?: string;
  textColor?: string;
  duration?: number; // 动画持续时间，单位毫秒
  showSplit?: boolean;
}


function BiStatisticsNum3(props: TBiStatisticsNum3Props) {



  const nums = useCreation(() => {
    const len = Math.max(props.value.toString().length, props.len || 5)
    return props.value.toString().padStart(len, '0').split('').map(item => parseInt(item))
  }, [props.value]);

  // console.log('nums', nums)

  return (
    <div className="flex flex-col items-center gap-1">
      <div className="flex gap-2">
        {nums.map((num, index) => <div className="flex gap-2" key={index}>
          <Flipper size={props.size} num={num}  bgColor={props.bgColor} textColor={props.textColor} />
          {props.showSplit && (nums.length - 1 - index) % 3 === 0 && index !== nums.length - 1 && (<div className="text-cyan-100 -mx-1 my-1" >,</div>)}
        </div>)}
      </div>
      {props.label ? <div className="text-cyan-100 opacity-80" style={{ fontSize: props.labelSize || 12 }}>{props.label}</div> : null}
    </div>
  )
}

export default BiStatisticsNum3
