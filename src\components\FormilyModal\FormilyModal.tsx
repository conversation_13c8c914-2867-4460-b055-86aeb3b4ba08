import { createForm, IFormProps, Form as TForm } from "@formily/core";
import { Modal } from "antd";
import { ForwardedRef, forwardRef, useImperativeHandle, useRef, useState } from "react";
import { SchemaField, TSchemaFieldProps } from '../Formily';
import { Form, FormButtonGroup, Reset, Submit } from "@formily/antd-v5";
import { useMemoizedFn } from "ahooks";
import pick from "lodash/pick";
import './FormilyModal.less';


export type TFormilyModalRef<TFormData extends object> = {
  show: (options: TFormilyModalShowOptions<TFormData>, formOptions?: IFormProps<TFormData>) => {
    form: TForm<TFormData>;
  },
  formRef: React.MutableRefObject<TForm<TFormData>>;
}

export type TFormilyModalShowOptions<TFormData> = {
  modalTitle?: string;
  modalWidth?: number;
  onCancel?: () => void;
  initialValues?: Partial<TFormData>;
  schema: TSchemaFieldProps['schema'],
  scope?: TSchemaFieldProps['scope'],
  onAutoSubmit?: (values: TFormData) => Promise<void>;
  extraContent?: React.ReactNode;
}

export type TFormilyModalProps = {
  formId: string;
}


function FormilyModalBase<TFormData extends object>(
  _props: TFormilyModalProps,
  ref: ForwardedRef<TFormilyModalRef<TFormData>>
) {
  const [options, setOptions] = useState<TFormilyModalShowOptions<TFormData>>();
  const [open, setOpen] = useState(false);
  const form = useRef(createForm<TFormData>({
    validateFirst: true,
  }))


  useImperativeHandle(ref, () => {
    return {
      show: (options: TFormilyModalShowOptions<TFormData>, formOptions: IFormProps<TFormData> = {}) => {
        setOptions(options);
        const newForm = createForm<TFormData>({
          validateFirst: true,
          ...formOptions,
        })

        // 裁剪对象，仅留下schema?.properties中定义的key，填充策略选择覆盖
        newForm.setInitialValues(
          pick(options.initialValues || {}, Object.keys(options.schema?.properties || {})),
          'overwrite'
        );
        form.current = newForm;

        setOpen(true);
        return { form: newForm };
      },
      formRef: form
    };
  });

  const onModalCancel = useMemoizedFn(() => {
    setOpen(false);
    if (options?.onCancel) {
      options.onCancel();
    }
  });

  const onAutoSubmit = useMemoizedFn(async (values: TFormData) => {
    if (options?.onAutoSubmit) {
      await options.onAutoSubmit(values);
    }
    onModalCancel();
  });

  return (
    options && form.current ? (
      <Modal
        title={options.modalTitle || '编辑表单'}
        width={options.modalWidth || 600}
        maskClosable={false}
        open={open}
        footer={false}
        onCancel={onModalCancel}
        className="FormilyModal"
        destroyOnClose
      >
        {options.extraContent || null}
        <Form
          form={form.current}
          labelCol={5}
          wrapperCol={16}
          onAutoSubmit={onAutoSubmit}
        >
          <SchemaField schema={options.schema} scope={options.scope || {}} />
          <FormButtonGroup.FormItem>
            <Submit>提交</Submit>
            <Reset>重置</Reset>
          </FormButtonGroup.FormItem>
        </Form>
      </Modal>
    ) : null
  );
}

const FormilyModal = forwardRef(FormilyModalBase);

export default FormilyModal;