import { CopyOutlined } from "@ant-design/icons";
import { App, Dropdown } from "antd";

export interface TTricycleBatchObjProps {
  batchObj: any;
  children?: React.ReactNode;
}

function TricycleBatchObj(props: TTricycleBatchObjProps) {
  const { message } = App.useApp();
  return props.batchObj ? (
    <Dropdown
      menu={{
        items: [
          {
            icon: <CopyOutlined />,
            label: `名称: ${props.batchObj.name}`,
            key: "name",
            hide: false,
          },
          {
            icon: <CopyOutlined />,
            label: `数量: ${props.batchObj.count}`,
            key: "count",
            hide: !props.batchObj.count,
          },
          {
            icon: <CopyOutlined />,
            label: `说明: ${props.batchObj.description}`,
            key: "description",
            hide: !props.batchObj.description,
          },
        ].filter((item) => !item.hide),
        onClick: (item) => {
          navigator.clipboard.writeText(props.batchObj[item.key]);
          message.success("复制成功");
        },
      }}
      arrow
    >
      <div className="cursor-pointer">
        {props.children ? (
          props.children
        ) : (
          <div className="text-blue-500">{props.batchObj.name}</div>
        )}
      </div>
    </Dropdown>
  ) : null;
}

export default TricycleBatchObj;
