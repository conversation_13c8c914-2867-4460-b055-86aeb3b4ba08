import { HttpUtils } from "@/utils/http";

/**
 * @returns TAdminUser 对象
 */
export function apiGetCurrentUser() {
  return HttpUtils.request<TAdminUser>({
    url: `/auth/currentUser`,
  })
}

/**
 * @returns TAdminLoginRes 对象
 */
export function apiLogin(data: TAdminLoginReq) {
  return HttpUtils.request<TAdminLoginRes>({
    url: `/auth/login`,
    method: 'POST',
    data,
  })
}

/**
 * 手动关闭指定账单并退回积分
 * @returns boolean 对象
 */
export function apiBillClose(billId: string, query?: { reason: string }) {
  return HttpUtils.request<boolean>({
    url: `/bill/close/${billId}`,
    method: 'POST',
    query,
  })
}

/**
 * 获取账单详情
 * @returns TBill 对象
 */
export function apiBillGetById(query?: { id: string }) {
  return HttpUtils.request<TBill>({
    url: `/bill/getById`,
    query,
  })
}

/**
 * 分页获取账单列表
 * @returns TBillPage 对象
 */
export function apiBillPageList(query?: { pageIndex: number; pageSize: number; id?: string; orderType?: string; orderId?: string; clientUserId?: string; clientUserKeyword?: string; payStatus?: string; wechatPayNo?: string; paidAtStart?: string; paidAtEnd?: string; createdAtStart?: string; createdAtEnd?: string }) {
  return HttpUtils.request<TBillPage>({
    url: `/bill/pageList`,
    query,
  })
}

/**
 * 手动触发定时任务
 * @returns boolean 对象
 */
export function apiBillTriggerAutoCloseTask() {
  return HttpUtils.request<boolean>({
    url: `/bill/triggerAutoCloseTask`,
    method: 'POST',
  })
}

/**
 * 更新账单信息
 * @returns TBill 对象
 */
export function apiBillUpdate(data: TBillUpdateReq) {
  return HttpUtils.request<TBill>({
    url: `/bill/update`,
    method: 'POST',
    data,
  })
}

/**
 * 批量创建消息（发送给多个用户）
 * @returns number 对象
 */
export function apiClientMessageBatchCreate(query?: { messageType?: string; clientUserIds: string[]; title: string; content: string; relatedEntityId?: string }) {
  return HttpUtils.request<number>({
    url: `/clientMessage/batchCreateSystemMessage`,
    method: 'POST',
    query,
  })
}

/**
 * 获取消息详情
 * @returns TClientMessage 对象
 */
export function apiClientMessageGetById(query?: { id: string }) {
  return HttpUtils.request<TClientMessage>({
    url: `/clientMessage/getById`,
    query,
  })
}

/**
 * 分页获取所有用户的消息列表
 * @returns TClientMessagePage 对象
 */
export function apiClientMessagePageList(data: TAdminMessagePageListReq) {
  return HttpUtils.request<TClientMessagePage>({
    url: `/clientMessage/pageList`,
    method: 'POST',
    data,
  })
}

/**
 * 获取客户详情
 * @returns TClientUser 对象
 */
export function apiClientUserGetById(query?: { id: string }) {
  return HttpUtils.request<TClientUser>({
    url: `/clientUser/getById`,
    query,
  })
}

/**
 * @returns TClientUser[] 对象
 */
export function apiClientUserGetByIds(query?: { ids: string[] }) {
  return HttpUtils.request<TClientUser[]>({
    url: `/clientUser/getByIds`,
    query,
  })
}

/**
 * 分页获取客户列表
 * @returns TClientUserPage 对象
 */
export function apiClientUserPageList(query?: { pageIndex: number; pageSize: number; id?: string; nickname?: string; mobile?: string; email?: string; wxOpenid?: string; keyword?: string; isVip?: boolean }) {
  return HttpUtils.request<TClientUserPage>({
    url: `/clientUser/pageList`,
    query,
  })
}

/**
 * 获取反馈建议列表
 * @returns TFeedbackPage 对象
 */
export function apiFeedbackPageList(query?: { pageIndex: number; pageSize: number; id?: string; status?: string; clientUserKeyword?: string; mobile?: string }) {
  return HttpUtils.request<TFeedbackPage>({
    url: `/feedback/list`,
    query,
  })
}

/**
 * 处理反馈建议
 * @returns TFeedback 对象
 */
export function apiFeedbackProcess(data: TFeedbackProcessReq) {
  return HttpUtils.request<TFeedback>({
    url: `/feedback/process`,
    method: 'POST',
    data,
  })
}

/**
 * 上传文件
 * @returns string 对象
 */
export function apiUpload() {
  return HttpUtils.request<string>({
    url: `/file/upload`,
    method: 'POST',
  })
}

/**
 * 手动为用户添加/扣减点数
 * @returns TClientUserPointsRecord 对象
 */
export function apiPointsRecordAdd(data: TAdminAddPointsReq) {
  return HttpUtils.request<TClientUserPointsRecord>({
    url: `/pointsRecord/addPoints`,
    method: 'POST',
    data,
  })
}

/**
 * 获取点数变化记录详情
 * @returns TClientUserPointsRecord 对象
 */
export function apiPointsRecordGetById(query?: { id: string }) {
  return HttpUtils.request<TClientUserPointsRecord>({
    url: `/pointsRecord/getById`,
    query,
  })
}

/**
 * 分页获取所有用户的点数变化记录列表
 * @returns TClientUserPointsRecordPage 对象
 */
export function apiPointsRecordPageList(query?: { pageIndex: number; pageSize: number; id?: string; clientUserId?: string; clientUserKeyword?: string; recordType?: string; relatedBillId?: string; createdAtStart?: string; createdAtEnd?: string }) {
  return HttpUtils.request<TClientUserPointsRecordPage>({
    url: `/pointsRecord/pageList`,
    query,
  })
}

/**
 * 获取VIP费用订单详情
 * @returns TVipFeeOrder 对象
 */
export function apiVipFeeOrderGetById(query?: { id: string }) {
  return HttpUtils.request<TVipFeeOrder>({
    url: `/vipFeeOrder/getById`,
    query,
  })
}

/**
 * 获取VIP费用订单分页列表
 * @returns TVipFeeOrderPage 对象
 */
export function apiVipFeeOrderPageList(query?: { pageIndex: number; pageSize: number; id?: string; clientUserId?: string; clientUserKeyword?: string; vipFeeTypeId?: string; status?: string }) {
  return HttpUtils.request<TVipFeeOrderPage>({
    url: `/vipFeeOrder/pageList`,
    query,
  })
}

/**
 * 创建会员费类型
 * @returns TVipFeeType 对象
 */
export function apiVipFeeTypeCreate(data: TVipFeeTypeCreateReq) {
  return HttpUtils.request<TVipFeeType>({
    url: `/vipFeeType/create`,
    method: 'POST',
    data,
  })
}

/**
 * 获取VIP费用类型分页列表
 * @returns TVipFeeTypePage 对象
 */
export function apiVipFeeTypePageList(query?: { id?: string; name?: string; isEnabled?: boolean; isPublic?: boolean; pageIndex: number; pageSize: number }) {
  return HttpUtils.request<TVipFeeTypePage>({
    url: `/vipFeeType/pageList`,
    query,
  })
}

/**
 * 更新会员费类型
 * @returns TVipFeeType 对象
 */
export function apiVipFeeTypeUpdate(data: TVipFeeTypeUpdateReq) {
  return HttpUtils.request<TVipFeeType>({
    url: `/vipFeeType/update`,
    method: 'POST',
    data,
  })
}

