import React, { forwardRef, useEffect, useImperativeHandle } from "react"
import anime from 'animejs';
import './BiAutoScrollView.less'

export interface TBiAutoScrollViewProps {
  height?: number
  itemHeight?: number
  delay?: number
  children: React.ReactNode
}

export type TBiAutoScrollViewRef = {
  toTop: () => void
}

const BiAutoScrollView = forwardRef<TBiAutoScrollViewRef, TBiAutoScrollViewProps>((props, ref) => {
  const animalTimeoutRef = React.useRef<NodeJS.Timeout>(null)
  const scrollViewRef = React.useRef<HTMLDivElement>(null)
  const toTopSkipRef = React.useRef(false)

  useImperativeHandle(ref, () => ({
    toTop: () => {
      toTopSkipRef.current = true
      anime({
        targets: {
          top: scrollViewRef.current!.scrollTop
        },
        top: 0,
        easing: 'easeInOutSine',
        round: 1,
        update: () => {
          scrollViewRef.current!.scrollTop = 0;
        },
      });
    }
  }))

  useEffect(() => {
    animalTimeoutRef.current && clearInterval(animalTimeoutRef.current);

    animalTimeoutRef.current = setInterval(() => {
      if(toTopSkipRef.current) {
        toTopSkipRef.current = false 
        return
      };
      if(!scrollViewRef.current) return;
      const { scrollHeight, clientHeight } = scrollViewRef.current;
      const itemHeight = props.itemHeight;

      const scrollProps = {
        top: Math.round(scrollViewRef.current!.scrollTop / itemHeight) * itemHeight,
      }
  
      const maxScrollTop = Math.round((scrollHeight - clientHeight) / itemHeight) * itemHeight;
  
      if (scrollProps.top % itemHeight !== 0) {
        scrollProps.top = Math.floor(scrollProps.top / itemHeight) * itemHeight;
      }
  
      if (maxScrollTop <= scrollProps.top) {
        scrollViewRef.current.scrollTop = 0;
        return;
      }

      anime({
        targets: scrollProps,
        top: Math.min(maxScrollTop, scrollProps.top + itemHeight),
        easing: 'easeInOutSine',
        round: 1,
        update: () => {
          scrollViewRef.current!.scrollTop = scrollProps.top;
        },
      });
    }, props.delay || 3000);

    return () => {
      animalTimeoutRef.current && clearInterval(animalTimeoutRef.current);
    };
  }, [props.delay])

  return (
    <div className="BiAutoScrollView" style={{ overflowY: 'auto', height: props.height}} ref={scrollViewRef}>
      {props.children}
    </div>
  )
})

export default BiAutoScrollView
