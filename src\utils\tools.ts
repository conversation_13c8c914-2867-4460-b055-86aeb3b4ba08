import { SortOrder } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import ExcelJS from 'exceljs';
import { Random } from 'tony-mockjs';
import QrcodeSvg from 'qrcode-svg'
import NP from 'number-precision';
import { AgreeDicts } from './agreeDict';

NP.enableBoundaryChecking(false);

export class ToolsUtil {

  /**
   * 优惠券面值
   * @param item 
   * @returns 
   */
  static couponParValue = (item: {
    parValue: number
    type: string
  }) => {
    if (item.type === '0') {
      if (item.parValue >= 60) {
        return {
          value: Number((item.parValue / 60).toFixed(2)).toString(),
          unit: '小时'
        }
      } else {
        return {
          value: item.parValue.toString(),
          unit: '分钟'
        }
      }
    } else if (item.type === '1') {
      return {
        value: Number((item.parValue / 100).toFixed(2)).toString(),
        unit: '元'
      }
    } else if (item.type === '2') {
      return {
        value: Number((item.parValue / 10).toFixed(2)).toString(),
        unit: '折'
      }
    }

    return {
      value: '',
      unit: ''
    }
  }

  static couponPar = (item: {
    parValue: number
    type: string
  }) => {
    return `${this.couponParValue(item).value}${this.couponParValue(item).unit}`
  }

  static couponPrice(price: number, couponObj: TCoupon | TCouponTpl) {
    if (!couponObj) return price;
    // { value: '0', label: '小时券' }, // 单位:分钟
    // { value: '1', label: '金额券' }, // 单位:分
    // { value: '2', label: '折扣券' }, // 0表示免费 10表示1折 85表示85折 90表示9折
    if (couponObj.type.toString() === AgreeDicts.Coupon_type.labelValueMap['金额券']) {
      return price - couponObj.parValue;
    }
    if (couponObj.type.toString() === AgreeDicts.Coupon_type.labelValueMap['折扣券']) {
      return price * couponObj.parValue / 100;
    }
  }

  /**
 * 近似，保留几位小数
 */
  static mathRound(num?: number, decimal = 2) {
    return NP.round(num || 0, decimal);
  }

  /**
   * 让精度丢失的数字正确
   */
  static mathStrip(num?: number) {
    return NP.strip(num || 0);
  }

  /**
   * 计算并转为字符串，用于展示数字
   **/
  static MathCalcAndToString({
    num,
    calcFun = (num) => num,
    decimal = 2,
    nansStr = '-',
    unit = '',
  }: {
    num: unknown;
    calcFun?: (num: number) => number;
    decimal?: number;
    nansStr?: string;
    unit?: string;
  }) {
    if (typeof num === 'string' && /^-?(0|([1-9][0-9]*))(\.[\d]+)?$/.test(num)) {
      num = Number(num);
    }
    if (typeof num !== 'number') {
      return nansStr
    };
    const result = calcFun(num);
    if (typeof result !== 'number') return nansStr;
    return `${this.mathRound(result, decimal)}${unit}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  /** 存成分的金额转字符串 */
  static moneyCentFormat(num: number, unit?: string) {
    return this.MathCalcAndToString({
      num,
      calcFun: (num) => num / 100,
      decimal: 2,
      unit: unit === undefined ? '元' : unit,
    });
  }

  /**
   * 等待一段时间
   * @param msTime 等待时间ms
   * @returns
   */
  static waitTime(msTime: number = 100) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(true);
      }, msTime);
    });
  }

  static random = Random;

  /**
   * 是否开发环境
   * @returns
   */
  static isDebug = import.meta.env.DEV;

  /**
   * 是否线上开发环境
   * @returns
   */
  static isDev = window.location.hostname === 'admin-dev.dnhhsj.dounanflowers.com';

  /**
   * 是否生产环境
   * @returns
   */
  static isProd = window.location.hostname === 'admin-prod.dnhhsj.dounanflowers.com';

  /**
   * 选项转枚举
   * @param options 选项列表
   * @returns
   */
  static optionsToEnum<T extends string | number>(options: { label: string; value: T }[]) {
    return (options || []).reduce(
      (before, current) => {
        before[current.value] = current.label;
        return before;
      },
      {} as Record<T, string>
    );
  }

  /**
   * 枚举转选项
   * @param enumObj 枚举对象
   * @returns
   */
  static enumToOptions<T extends string | number>(enumObj: Record<T, string>) {
    return Object.keys(enumObj).map((key) => {
      return {
        label: enumObj[key],
        value: key as T,
      };
    });
  }

  /**
   * 时间格式化
   * @param time
   * @param options
   * @returns
   */
  static timeFormat(
    time?: number | string | Date | null | undefined,
    options?: {
      format?: string;
      formatType?: 'time' | 'date';
      emptyStr?: string;
    }
  ) {
    const { formatType = 'time', emptyStr = '-' } = options || {};
    if (!time) return emptyStr;
    const format = options?.format || (formatType === 'time' ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD');
    return dayjs(time).format(format);
  }

  /**
   * 递归把treeOptions转成方便查找的数据结构
   * @param treeOptions
   * @returns
   */
  static getDictOptionsData(options: TDictOption[]) {
    const newOptions = JSON.parse(JSON.stringify(options || []));
    function tranToValueDataMap(treeOptions: TTreeOption[], parentValues: string[] = [], parentChildrenValues: string[] = []) {
      let result = {};
      // 过滤掉status为false的选项
      treeOptions.push(...treeOptions.splice(0, treeOptions.length).filter((item) => item.status));
      for (const item of treeOptions) {
        item.key = item.value;
        parentChildrenValues.push(item.value);
        const childrenValues = [];
        if (item.children) {
          const childrenMap = tranToValueDataMap(item.children, [...parentValues, item.value], childrenValues);
          result = { ...result, ...childrenMap };
          parentChildrenValues.push(...childrenValues);
        }
        result[item.value] = {
          ...item,
          parentValues: [...parentValues],
          childrenValues,
          upValues: [...parentValues, item.value],
          downValues: [item.value, ...childrenValues],
        };
      }
      return result as Record<
        string,
        TTreeOption & {
          parentValues: string[];
          childrenValues: string[];
          upValues: string[];
          downValues: string[];
        }
      >;
    }
    const valueDataMap = tranToValueDataMap(newOptions);
    return {
      baseOptions: options,
      options: newOptions,
      values: Object.keys(valueDataMap),
      labels: Object.values(valueDataMap).map((item) => item.label),
      valueLabelMap: Object.values(valueDataMap).reduce((prev, item) => {
        return { ...prev, [item.value]: item.label };
      }, {}),
      labelValueMap: Object.values(valueDataMap).reduce((prev, item) => {
        return { ...prev, [item.label]: item.value };
      }, {}),
      valueDataMap,
      labelDataMap: Object.values(valueDataMap).reduce((prev, item) => {
        prev[item.label] = item;
        return prev;
      }, {}),
    };
  }

  /**
   * 把传入的时间范围转成毫秒时间戳时间范围
   * @param range 时间范围，开头和结束都可以设置为null
   * @returns
   */
  static dateRangeToFilterRange(range: [Date | string | null, Date | string | null]) {
    return [
      range[0] ? dayjs(range[0]).startOf('day').valueOf() : 0,
      dayjs(range[1] ? range[1] : '2099-12-31')
        .endOf('day')
        .valueOf(),
    ] as [number, number];
  }

  /**
   * 把表格request的 sorter转成分页请求的sort
   * @param sorter
   * @returns
   */
  static tableSorterToPageListReqSort<T>(sorter: Record<string, SortOrder>) {
    const sort = sorter
      ? Object.keys(sorter).map((key: keyof TShop) => ({ field: key, order: sorter[key] === 'ascend' ? 'ASC' : 'DESC' }))
      : [];
    return sort as TSortItem<T>[];
  }

  /**
   * 检查树形结构中是否存在相同的键值和键名
   * @param options
   */
  static optionsCheckUnique(options: TTreeOption[]) {
    const valuesSet = new Set<string>();
    const labelsSet = new Set<string>();
    const check = (_options: TTreeOption[]) => {
      for (const item of _options) {
        if (valuesSet.has(item.value)) {
          throw {
            message: '存在相同的键值',
            data: item.value,
          };
        }
        if (labelsSet.has(item.label)) {
          throw {
            message: '存在相同的键名',
            data: item.label,
          };
        }
        valuesSet.add(item.value);
        labelsSet.add(item.label);
        if (item.children) {
          check(item.children);
        }
      }
    };
    check(options);
  }

  /** 拍卖状态 */
  static auctionStatus(auctionObj?: TAuction) {
    if (!auctionObj) {
      return '';
    }
    const now = dayjs().valueOf();
    if (auctionObj.manualStopAt) {
      return '已作废';
    }
    if (now < dayjs(auctionObj.startAt).valueOf()) {
      return '待开拍';
    }
    if (!auctionObj.endAt) {
      return '竞价中';
    }
    if (auctionObj.endAt && !auctionObj.dealUserId) {
      return '已流拍';
    }

    if (auctionObj.tailBreakAt) {
      return '尾款违约';
    }

    if (auctionObj.deliveredAt) {
      return '已交割';
    }
    if (!auctionObj.deliveredAt && auctionObj.tailPaidAt) {
      return '待交割';
    }
    if (!auctionObj.tailPaidAt && !auctionObj.tailBreakAt && now > dayjs(auctionObj.tailBreakDefaultAt).valueOf()) {
      return '尾款超期';
    }
    if (!auctionObj.tailPaidAt && !auctionObj.tailBreakAt && now <= dayjs(auctionObj.tailBreakDefaultAt).valueOf()) {
      return '待付尾款';
    }
  }

  /** 下载文件 */
  static downloadFile(url: string) {
    const a = document.createElement('a');
    a.href = url;
    a.target = '_blank';
    const name = url.substring(url.lastIndexOf('/') + 1);
    a.download = name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }

  static svgToJpgBlog(svg: string) {
    return new Promise<Blob>((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.drawImage(img, 0, 0);
          canvas.toBlob((blob) => {
            resolve(blob as Blob);
          }, 'image/jpeg');
        } else {
          reject();
        }
      };
      img.src = `data:image/svg+xml;utf8,${encodeURIComponent(svg)}`;
    });
  }

  static downloadBlob(blob: Blob, name: string) {
    const localUrl = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = localUrl;
    a.download = name;
    a.click();
    URL.revokeObjectURL(localUrl);
  }

  static async exportExcel<T = any>(options: {
    name: string;
    data: T[];
    columns: {
      title: string;
      width?: number;
      dataIndex: string;
      render?: (record: T) => string | Date | number;
      valueEnum?: { [key: string]: string };
    }[];
  }) {
    const { name, data, columns } = options;
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(name);
    // const titleRow = columns.map((item) => item.title);
    // worksheet.addRow(titleRow);
    worksheet.columns = columns.map(item => {
      return {
        header: item.title,
        key: item.dataIndex,
        width: item.width || 10,
      };
    });
    data.forEach((item) => {
      const row = columns.map((col) => {
        if (col.render) {
          return col.render(item);
        }
        if (col.valueEnum) {
          return col.valueEnum[item[col.dataIndex]] || item[col.dataIndex];
        }
        return item[col.dataIndex];
      });
      worksheet.addRow(row);
    });
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    ToolsUtil.downloadBlob(blob, `${name}.xlsx`);
  }



  // 进入全屏
  static fullScreen(el = document.documentElement) {
    el.requestFullscreen();
  }
  // 退出全屏
  static exitFullScreen() {
    document.exitFullscreen();
  }

  /**
   * 生成车辆二维码svg
   * @param tricycle 
   * @returns 
   */
  static tricycleQrcodeSvg(tricycle: TTricycle) {
    // console.log('%c [ tricycle ]-341', 'font-size:13px; background:pink; color:#bf2c9f;', tricycle)
    const numCode = tricycle.number || tricycle.numCode
    const id = tricycle._id || tricycle.id
    const svgPaths = new QrcodeSvg({
      width: 512,
      height: 512,
      padding: 2,
      content: `https://shopminicode.dounanflowers.com?target=tricycle&id=${id}`,
    }).svg({ container: 'none' });
    const svg = `<?xml version="1.0" standalone="yes"?>
<svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="512" height="620">
<rect x="0" y="0" width="512" height="620" style="fill:#ffffff"/>
${svgPaths}
<text x="256" y="600" font-size="135" style="text-anchor: middle">${numCode}</text>
</svg>`
    return svg
  }

  static getWeekDay(diff = 0) {
    return (new Date().getDay() - 1 + 7 * 10000 + diff) % 7
  }

  static jobSalaryStr = (item: {
    salaryType: string
    salary: number
    salaryMax?: number
  }, scale = 1) => {

    const jobSalaryStr = item.salaryMax ? `${this.moneyCentFormat(item.salary * scale, '')} - ${this.moneyCentFormat(item.salaryMax * scale, '')}` : this.moneyCentFormat(item.salary * scale, '')
    switch (item.salaryType.toString()) {
      case '0':
        return '面议'
      case '1':
        return jobSalaryStr + '元/次'
      case '2':
        return jobSalaryStr + '元/时'
      case '3':
        return jobSalaryStr + '元/天'
      case '4':
        return jobSalaryStr + '元/月'
      default:
        return ''
    }
  }

  static parkSerivcePriceStr = (item: {
    priceType: string
    price: number
    priceMax?: number
  }) => {

    const jobSalaryStr = item.priceMax ? `${this.moneyCentFormat(item.price, '')} - ${this.moneyCentFormat(item.priceMax, '')}` : this.moneyCentFormat(item.price, '')
    switch (item.priceType?.toString()) {
      case '0':
        return '面议'
      case '1':
        return jobSalaryStr + '元/次'
      case '2':
        return jobSalaryStr + '元/时'
      case '3':
        return jobSalaryStr + '元/天'
      case '4':
        return jobSalaryStr + '元/月'
      default:
        return ''
    }
  }

  static handleHtml = (html: string) => {
    if (!html) return ''
    const newContent = html.replace(/<img[^>]*>/gi, function (match) {
      return match.replace(/(style="(.*?)")|(width="(.*?)")|(height="(.*?)")/ig, '');
    });
    return newContent.replace(/\<img/gi, '<img style="max-width:100%;height:auto;" ')?.replace(/style=\"\"/gi, '');
  }


}
