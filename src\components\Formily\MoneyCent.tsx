import NumUtils from '@/utils/num';
import {
  connect,
  mapReadPretty,
} from '@formily/react'
import { InputNumber } from 'antd';


const MonetCent = (props: any) => {
  return (
    <InputNumber
      value={NumUtils.strip(props.value / 100)}
      onChange={(newValue) => props.onChange(NumUtils.strip(newValue * 100))}
      formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
      parser={(value) => value?.replace(/\$\s?|(,*)/g, '') as unknown as number}
      addonAfter='元'
      className='w-full'
    />
  );
};

export default connect(
  MonetCent,
  mapReadPretty(({value}) => <div className='text-bold'>
    {typeof value === 'number' ? NumUtils.strip(value / 100).toFixed(2) + '元' : '-'}
  </div>),
);
