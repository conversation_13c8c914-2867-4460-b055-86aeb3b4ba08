
import { useAsyncEffect, useCreation, useSet } from "ahooks";
import BiAutoScrollView, { TBiAutoScrollViewRef } from "../BiAutoScrollView";
import NumUtils from "@/utils/num";
import BiStatisticsNum3 from "../BiStatisticsNum3";
import { useEffect, useRef, useState } from "react";
import { biTradingModel } from "@/stores/biTradingModel";

const token = "34dec3d76836c0b1f0125eaccc7aec3e"

type TOrder = {
    finalpaid: string
    supplier_name: string
    buyer_name: string
    type: string
    price: number
    id: number
    created: string
    ordernum: string
}

const Bi01TradingTrend2 = () => {
    const scrollViewRef = useRef<TBiAutoScrollViewRef>(null)
    const [orderList, setOrderList] = useState<TOrder[]>([])
    const [orderTotal, setOrderTotal] = useState(0)
    const [amountTotal, setAmountTotal] = useState(0)
    const [newOrderIdSet, { add: addNewOrderId, remove: removeNewOrderId }] = useSet<string>()


    useEffect(() => {
        let ws = IR.IRWSV2({
            channel: 'i158996526485',
            wsurl: "wss://irapi.inruan.com/ws/",
            autoping: true,
            subscribe: {
                hsp(d: any) {
                    console.log('hsp', d)
                    const newOrders: TOrder[] = d?.data?.items || [];
                    if (newOrders.length > 0) {
                        setOrderList(orderList => [...newOrders, ...orderList])
                        setOrderTotal(d?.data?.total_count || 0)
                        setAmountTotal(Math.round(d?.data?.total_price || 0))
                        newOrders.forEach(item => {
                            addNewOrderId(item.ordernum)
                            setTimeout(() => {
                                removeNewOrderId(item.ordernum)
                            }, 3000)
                        })
                    }

                },
                refresh() {
                    IR.reloadURL();
                }
            }
        });
        ws.attach({ token });
        return () => {
            ws.close();
        }
    }, [])

    useAsyncEffect(async () => {
        const response = await fetch('https://m.huashangpay.cn/ex/screen/api/getOrders', {
            method: 'POST',
            body: JSON.stringify({ token }),
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();
        setOrderList(data.data.items)
        setOrderTotal(data.data.total_count)
        setAmountTotal(Math.round(data.data.total_price))
    }, [])

    useEffect(() => {
        scrollViewRef.current.toTop()
    }, [orderList])

    useEffect(() => {
        biTradingModel.setState({
            trading2AmountTotal: amountTotal
        })
    }, [amountTotal])

    const data = useCreation(() => {
        return orderList.map(item => {
            return {
                key: item.ordernum,
                id: (item.ordernum || '').slice(-3) + '***',
                user: item.supplier_name + ' > ' + item.buyer_name,
                type: item.type,
                moneyCent: item.price * 100,
                time: item.created,
                // price: item.price
            }
        })
    }, [orderList])


    return (
        <div className="relative w-full flex flex-col" >
            <div className="mb-4 flex flex-col gap-1">
                <div className="flex ">
                    <div className="w-140px shrink-0 text-cyan-100">累计开单数(单)</div>
                    <BiStatisticsNum3 value={orderTotal || 0} size={14} showSplit />
                </div>
                <div className="flex ">
                    <div className="w-140px shrink-0 text-cyan-100">累计开单金额(元)</div>
                    <BiStatisticsNum3 value={amountTotal || 0} size={14} showSplit />
                </div>

            </div>
            <BiAutoScrollView itemHeight={70} height={240} delay={3000} ref={scrollViewRef}>
                {(data || []).map((item, index) => (
                    <div key={index}
                        className='relative h-60px w-full mb-10px text-white px-10px  rounded-md flex flex-col justify-center'
                        style={{
                            transition: 'all 1s ease-in-out',
                            background: newOrderIdSet.has(item.key) ? '#FDBF4433' : '#15E1FD22',
                        }}
                    >
                        <div className='absolute left-0 top-1/2 -translate-y-1/2 w-2px h-20px bg-green-500'></div>
                        <div className="flex flex-col gap-0.5  text-[10px] text-[#ffffff55]">
                            <div>{item.id}</div>
                            <div className='text-[#ffffff88] text-xs'>{item.type}</div>
                            <div className="flex justify-between items-center">
                                <span>{item.user}</span>
                                <span>{item.time}</span>
                            </div>
                        </div>
                        <div className='text-cyan-500 absolute right-10px top-10px'>¥ {NumUtils.moneyCentFormat(item.moneyCent, '')}</div>
                    </div>
                ))}
            </BiAutoScrollView>
        </div>
    );
};

export default Bi01TradingTrend2;