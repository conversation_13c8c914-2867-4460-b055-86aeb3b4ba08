import { fetchXml, XmlData } from 'iconfont-parser';
import fs from 'fs';
import path from 'path';
import https from 'https'



interface IconInfo {
  name: string
  unicode: string
}

async function downloadFile(url: string, dest: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(dest)
    https.get('https:' + url, (response) => {
      response.pipe(file)
      file.on('finish', () => {
        file.close()
        resolve()
      })
    }).on('error', (err) => {
      fs.unlink(dest, () => { })
      reject(err)
    })
  })
}

function extractFontUrls(css: string): string[] {
  const urlRegex = /url\('([^']+\.ttf)/g
  const urls: string[] = []
  let match
  while ((match = urlRegex.exec(css)) !== null) {
    urls.push(match[1])
  }
  return urls
}

function extractIconInfo(css: string): IconInfo[] {
  const iconRegex = /\.icon-([^:]+):before\s*{\s*content:\s*"\\([^"]+)"/g
  const icons: IconInfo[] = []
  let match
  while ((match = iconRegex.exec(css)) !== null) {
    icons.push({
      name: match[1],
      unicode: match[2]
    })
  }
  return icons
}


function generateTsComponent(icons: IconInfo[]): string {
  const iconNames = icons.map(icon => `'${icon.name}'`).join(' | ')
  const template = `import { createMemo, mergeProps, Show } from "solid-js"
import { Text } from "@tarojs/components"
import './Iconfont.scss'

export interface TIconfontProps {
  class?: string;
  style?: any;
  name: ${iconNames} | '';
  color?: string | string[] | Record<number, string>
  size?: number
}

export const Iconfont = (_props: TIconfontProps) => {
  const props = mergeProps({}, _props)


  const style = createMemo(() => {
    const res: any = props.style || {}
    if(props.color) {
      res.color = props.color
    }
    if(props.size) {
      res['font-size'] = props.size + 'rpx'
    }
    return res
  })

  return (
    <Show when={props.name} >
      <Text class={\`iconfont icon-\${props.name} \${props.class || ''}\`} style={style()} />
    </Show>
  )
}

`
  return template
}

function generateScssComponent(icons: IconInfo[]): string {
  const aa = icons.map(icon => `.icon-${icon.name}:before { content: "\\${icon.unicode}"; }`).join('\n')
  return `@font-face {
  font-family: "iconfont"; /* Project id 4909549 */
  src: url('./iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
}

${aa}
`
}

async function main() {

  const iconUrl = process.argv[2]
  if (!iconUrl) {
    console.error('请提供iconfont CSS URL')
    process.exit(1)
  }

  
  const iconfontDirPath = path.join('./src/components', 'Iconfont')

  if (!fs.existsSync(iconfontDirPath)) {
    fs.mkdirSync(iconfontDirPath, { recursive: true });
  }

  console.log('正在读取CSS文件...')
  const res = await fetch('https:' + iconUrl)
  const css = await res.text()
  const icons = extractIconInfo(css)

  // 下载字体文件
  console.log('正在下载字体文件...')
  await downloadFile(iconUrl.replace('.css', '.ttf'), path.join(iconfontDirPath, "iconfont.ttf"))

  console.log('正在生成SCSS文件...')
  const scssContent = generateScssComponent(icons)
  fs.writeFileSync(path.join(iconfontDirPath, 'Iconfont.scss'), scssContent)

  console.log('正在生成组件文件...')
  const iconfontFileContent = generateTsComponent(icons)
  fs.writeFileSync(path.join(iconfontDirPath, 'Iconfont.tsx'), iconfontFileContent)

}
main()

