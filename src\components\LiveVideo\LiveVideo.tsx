import { useEffect, useRef, useImperativeHandle, forwardRef, useState } from 'react';
import Hls from 'hls.js';
import { useCreation } from 'ahooks';
import urlParse from 'url-parse';

export interface LiveVideoProps {
  src: string;
  width?: string | number;
  height?: string | number;
  autoPlay?: boolean;
  muted?: boolean;
  className?: string;
  onError?: (error: any) => void;
  showTitle?: boolean
}

export interface LiveVideoRef {
  play: () => Promise<void>;
  pause: () => void;
  getVideoElement: () => HTMLVideoElement | null;
}

const LiveVideo = forwardRef<LiveVideoRef, LiveVideoProps>(({
  src,
  width = '100%',
  height = '100%',
  autoPlay = true,
  muted = true,
  className = '',
}, ref) => {

  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const hlsRef = useRef<Hls | null>(null);
  const dahuaPlayerRef = useRef<any>(null);


  const type = useCreation(() => {
    if ((src || '').includes('m3u8')) return 'm3u8';
    if ((src || '').includes('dahua')) return 'dahua';
    if ((src || '').includes('.mp4')) return 'mp4';
    return '';
  }, [src]);

  useEffect(() => {
    if (type !== 'm3u8' || !videoRef.current || !src) return;
    const video = videoRef.current;

    const initHls = () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
      }

      const hls = new Hls({
        debug: false,
        enableWorker: true,
      });

      hls.attachMedia(video);
      hls.on(Hls.Events.MEDIA_ATTACHED, () => {
        hls.loadSource(src);
      });

      hls.on(Hls.Events.ERROR, (event, data) => {
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              hls.startLoad();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              hls.recoverMediaError();
              break;
            default:
              initHls();
              break;
          }
        }
      });

      hlsRef.current = hls;
    };

    if (Hls.isSupported()) {
      initHls();
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // 对于原生支持 HLS 的浏览器（如 Safari）
      video.src = src;
    }

    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };
  }, [src, type]);

  useEffect(() => {
    if (type !== 'dahua' || !videoRef.current || !src) return;
    const video = videoRef.current;
    const canvas = canvasRef.current;

    const url = urlParse(src, true)

    const prefix = src.startsWith('dahuas') ? 'wss://' : 'ws://';

    const options = {
      wsURL: `${prefix}${url.host}${url.pathname}/rtspoverwebsocket`,
      rtspURL: `rtsp://${url.host}${url.pathname}/cam/realmonitor?channel=1&subtype=1&proto=Private3`,
      username: url.query.user,
      password:  url.query.password,
    }

    const player = new PlayerControl(options)
    player.on('WorkerReady', function () {
      player.connect()
    })
    player.init(canvas, video) //初始化播放器
    dahuaPlayerRef.current = player

    return () => {
      // player.
      // if (hlsRef.current) {
      //   hlsRef.current.destroy();
      //   hlsRef.current = null;
      // }
    };
  }, [src, type]);

  useImperativeHandle(ref, () => ({
    play: async () => {
      if (videoRef.current && type === 'm3u8') {
        await videoRef.current.play();
      }
      if (dahuaPlayerRef.current && type === 'dahua') {
        dahuaPlayerRef.current.play()
      }
    },
    pause: () => {
      if (videoRef.current && type === 'm3u8') {
        videoRef.current.pause();
      }
      if (dahuaPlayerRef.current && type === 'dahua') {
        dahuaPlayerRef.current.stop()
      }
    },
    getVideoElement: () => videoRef.current
  }));

  return (
    <div style={{ width, height, overflow: 'hidden', borderRadius: '8px', backgroundColor: '#000' }}>
      <video
        ref={videoRef}
        className={className}
        style={{ 
          width, 
          height,  
          display: type === 'dahua' ? 'none' : 'block',
          objectFit: 'cover'
        }}
        autoPlay={autoPlay}
        muted={muted}
        playsInline
        loop={type === 'mp4'}
        controls={type !== 'mp4'}
      >
        {type === 'mp4' && <source src={src} type="video/mp4" />}
        您的浏览器不支持 HTML5 video 标签。
      </video>
      <canvas ref={canvasRef} style={{ width, height, display: type === 'dahua' ? 'block' : 'none' }} />
    </div>

  );
});

export default LiveVideo;
