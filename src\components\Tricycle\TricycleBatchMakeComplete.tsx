import { useMemoizedFn } from "ahooks";
import { App, Modal, Table } from "antd";
import { forwardRef, useImperativeHandle, useState } from "react";
import { apiTricycleBatchMakeComplete, apiTricyclePageList } from "@/apis/apis.api";
import { downloadBatchQrcodes } from "./utils";
import TricycleBatchObj from "../TricycleBatchObj";
import PreviewFileList from "../PreviewFileList";
import UserObj from "../UserObj";
import { AgreeDicts } from "@/utils/agreeDict";

export type TTricycleBatchMakeCompleteProps = {
    afterSubmit?: () => void
}

export type TTricycleBatchMakeCompleteRef = {
    show: (filter?: TFilterItem<TTricycle>[]) => void
}

const TricycleBatchMakeComplete = forwardRef<TTricycleBatchMakeCompleteRef, TTricycleBatchMakeCompleteProps>((props, ref) => {
    const { message, modal } = App.useApp()

    const [open, setOpen] = useState(false)
    const [selectedRowIds, setSelectedRowIds] = useState<string[]>([]);
    const [makeList, setMakeList] = useState<TTricycle[]>([]);

    const handleBatchMake = useMemoizedFn(async () => {
        if (selectedRowIds.length === 0) {
            message.warning('请选择制作完成的三轮车');
            return;
        }
        const confirm = await modal.confirm({
            title: '批量制作完成确认',
            content: `共计变更${selectedRowIds.length}条数据`,
        })
        if (confirm) {
            const res = await apiTricycleBatchMakeComplete(selectedRowIds);
            downloadBatchQrcodes(res)
            message.success(`提交${selectedRowIds.length}条，成功${res.count}条`);
            setOpen(false);
            setSelectedRowIds([]);
            props.afterSubmit && props.afterSubmit()
        }
    })

    useImperativeHandle(ref, () => ({
        show: async (filter?: TFilterItem<TTricycle>[]) => {
            const response = await apiTricyclePageList({
                pageNum: 1,
                pageSize: 10000,
                filter: [
                  ...(filter || []),
                  { type: 'eq', field: 'checkStatus', value: AgreeDicts.Tricycle_checkStatus.labelValueMap['制作中'] },
                ]
              });
              setMakeList(response.list || []);
            setOpen(true)
        },
    }));
    return (
        <Modal
            title={`批量制作完成(${selectedRowIds.length}条)`}
            open={open}
            onCancel={() => {
                setOpen(false);
                setSelectedRowIds([]);
            }}
            onOk={handleBatchMake}
            width={1000}
        >
            <Table
                rowKey="_id"
                size="small"
                dataSource={makeList}
                rowSelection={{
                    type: 'checkbox',
                    selectedRowKeys: selectedRowIds,
                    onSelect: (record, selected) => {
                        const s = new Set(selectedRowIds)
                        if (selected) {
                            s.add(record._id)
                        } else {
                            s.delete(record._id)
                        }
                        setSelectedRowIds(Array.from(s))
                    },
                    onSelectAll: (selected) => {
                        setSelectedRowIds(selected ? makeList.map(item => item._id) : [])
                    }
                }}
                columns={[
                    {
                        title: '三轮车编号',
                        dataIndex: 'numCode',
                        key: 'numCode',
                    },
                    {
                        title: '批次', dataIndex: 'batchObj',
                        render: (_text, record) => <TricycleBatchObj batchObj={record.batchObj} />,
                    },
                    {
                        title: '图片',
                        dataIndex: 'images',
                        render: (_text, record) => <PreviewFileList urls={record.images} type="button" />,
                    },
                    {
                        title: '商家用户',
                        dataIndex: 'merchantUserObj',
                        render: (_text, record) => <UserObj userObj={record.merchantUserObj} />,
                    },
                    {
                        title: '状态',
                        dataIndex: 'checkStatus',
                        render: (_text, record) => AgreeDicts.Tricycle_checkStatus.valueLabelMap[record.checkStatus],
                    },
                    { title: '创建时间', dataIndex: 'createdAt' },
                ]}
            />
        </Modal>
    )
})

export default TricycleBatchMakeComplete