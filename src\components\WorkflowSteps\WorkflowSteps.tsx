import { useMemo } from "react"
import { CalendarOutlined, PushpinOutlined } from "@ant-design/icons";
import { ToolsUtil } from "@/utils/tools";
import { Dropdown } from "antd";
import { AgreeDicts } from "@/utils/agreeDict";

export interface TWorkflowStepsProps {
  workflow: TBpmInstanceBo
}

function WorkflowSteps(props: TWorkflowStepsProps) {
  const steps = useMemo(() => {
    if (!props.workflow) return []
    const nodes = []
    for (let i = 0; i < props.workflow.nodes.length; i++) {
      if (i !== 0) {
        nodes.push(<div key={`${i}_`}>➡️</div>)
      }
      nodes.push(
        <Dropdown
          key={i}
          menu={{
            items: [
              {
                key: 'doneAt',
                icon: <CalendarOutlined />,
                label: ToolsUtil.timeFormat(props.workflow.nodes[i].operatedAt),
              },
              {
                key: 'resultText',
                icon: <PushpinOutlined />,
                label: AgreeDicts.Bpm_nodeStatus.valueLabelMap[props.workflow.nodes[i].status || ''] || '未知',
              },
            ],
          }}
          arrow
        ><a key={i}>{props.workflow.nodes[i].nodeName}</a>
        </Dropdown>
      )
    }
    return nodes
  }, [props.workflow])
  return (
    <div className="WorkflowSteps flex flex-wrap" >
      {steps}
    </div>
  )
}

export default WorkflowSteps
