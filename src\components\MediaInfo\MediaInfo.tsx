
import { apiMediaUpdate } from '@/apis/apis.api';
import { useDebounceFn, useMemoizedFn } from 'ahooks';
import { Image, Input } from 'antd';
import prettyBytes from 'pretty-bytes'
import { useEffect, useState } from 'react';

type MediaInfoProps = {
  media: TMedia,
  onChange: (newValue: TMedia) => void
}

function MediaInfo({
  media,
  onChange
}: MediaInfoProps) {

  const [innerMedia, setInnerMedia] = useState(media)

  const updateMeidaInfo = useMemoizedFn(async (data: Partial<TMedia>) => {
    const res = await apiMediaUpdate({
      ...data,
      _id: media._id
    })
    onChange && onChange(res)
  })

  const { run: updateDesc } = useDebounceFn((desc: string) => {
    updateMeidaInfo({ desc })
  }, { wait: 500 })

  useEffect(() => {
    updateDesc(innerMedia.desc)
  }, [innerMedia.desc])

  useEffect(() => {
    setInnerMedia(media)
  }, [media])

  return (
    <div className="MediaInfo">
      <div className="flex justify-center items-center w-full h-[200px] bg-black">
        {media.type === 'image' ? (
          <Image
            className="w-full max-h-[200px] mx-auto"
            src={`${media.url}?x-oss-process=image/resize,w_400`}
            preview={{
              src: media.url,
            }}
          />
        ) : null}
        {media.type === 'video' ? (
          <video
            className='w-full max-h-[200px] mx-auto'
            muted
            controls
            src={media.url}
            onLoadedMetadata={(event: any) => {
              if (!media.duration) {
                setInnerMedia({
                  ...media,
                  duration: event.target.duration,
                  width: event.target.videoWidth,
                  height: event.target.videoHeight,
                })
                updateMeidaInfo({
                  duration: event.target.duration,
                  width: event.target.videoWidth,
                  height: event.target.videoHeight,
                })
              }
            }}
          />
        ) : null}


      </div>
      <div className='flex flex-col gap-2 py-4'>
        <div className='break-all'><b>原文件名：</b>{media.name}</div>
        <div className='break-all'><b>资源地址：</b>{media.url}</div>
        <div className='break-all'><b>文件大小：</b>{prettyBytes(media.size)}</div>
        <div className='break-all'><b>像素尺寸：</b>{innerMedia.width} x {innerMedia.height}</div>
        {media.type === 'video' ? <div className='break-all'><b>视频时长：</b>{innerMedia.duration}秒</div> : null}
        <div className='break-all'><b>描述备注：</b>
          <Input.TextArea
          className='mt-2'
            value={innerMedia.desc}
            onChange={e => setInnerMedia({
              ...innerMedia,
              desc: e.target.value
            })} />
        </div>
      </div>

    </div>
  )
}

export default MediaInfo
