import { apiVipFeeTypeCreate, apiVipFeeTypeUpdate } from '@/apis/apis';
import { useFormModal } from '@/components/FormilyModal';
import { useMemoizedFn } from 'ahooks';
import { App } from 'antd';

interface TVipFeeTypeFormData {
  name: string;
  days: number;
  amountCent: number;
  isEnabled: boolean;
  isPublic: boolean;
}

export const useVipFeeTypeFormModal = (cb: () => void) => {
  const { message } = App.useApp();
  const { formModalRef, formModalHolder } = useFormModal<TVipFeeTypeFormData>();


  const showEditModal = useMemoizedFn(async (record?: TVipFeeType) => {
    formModalRef.current?.show({
      modalTitle: '编辑会员费类型',
      initialValues: record ? {
        name: record.name,
        amountCent: record.amountCent,
        days: record.days,
        isEnabled: record.isEnabled,
        isPublic: record.isPublic,
      } : undefined,
      modalWidth: 800,
      onAutoSubmit: async (values: TVipFeeTypeFormData) => {
       if(record){
        await apiVipFeeTypeUpdate({
          ...values,
          id: record.id,
        });
       }else{
        await apiVipFeeTypeCreate(values);
       }
        message.success("更新成功");
        cb();
      },
      schema: {
        type: "object",
        properties: {
          name: {
            title: "名称",
            type: "string",
            required: true,
            "x-decorator": "FormItem",
            "x-component": "Input",
          },
          amountCent: {
            title: "价格",
            type: "number",
            required: true,
            "x-decorator": "FormItem",
            "x-component": "MoneyCent",
          },
          days: {
            title: "有效天数",
            type: "number",
            required: true,
            "x-decorator": "FormItem",
            "x-component": "NumberPicker",
            "x-component-props": {
              min: 1,
              precision: 0,
            }
          },
          isEnabled: {
            title: "是否启用",
            type: "boolean",
            "x-decorator": "FormItem",
            "x-component": "Switch",
          },
          isPublic: {
            title: "是否公开",
            type: "boolean",
            "x-decorator": "FormItem",
            "x-component": "Switch",
          },
        },
      },
    });
  });

  return {
    showEditModal,
    formModalHolder,
  };
};
