.TabNav {
  &__tabs {
    display: flex;
    // background: #f3f3f3;
    background: var(--theme-background);
    padding: 0 20px;
    overflow-x: auto;
    &__item {
      cursor: pointer;
      height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      user-select: none;
      color: #aaa;
      flex-shrink: 0;
      &--active {
        position: relative;
        color: var(--primary-color);
        background: var(--theme-background-2);
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 2px;
          background: var(--primary-color);
        }
      }
      &__title {
        padding: 0 12px;
        height: 100%;
        display: flex;
        align-items: center;
        width: 100%;
      }
      &__btn {
        width: 20px;
        height: 20px;
        right: 0;
        text-align: center;
        line-height: 20px;
        margin: 0 4px 0 -4px;
        font-size: 12px;
      }
    }
  }
}