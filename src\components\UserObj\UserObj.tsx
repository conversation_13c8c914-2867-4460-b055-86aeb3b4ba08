import { CopyOutlined } from "@ant-design/icons";
import { App, Dropdown } from "antd";

export interface TUserObjProps {
  userObj: TClientUser | TAdminUser | any;
  children?: React.ReactNode;
  extraItems?: { icon: React.ReactNode, label: string, key: string, hide?: boolean, value?: string }[]
}

function UserObj(props: TUserObjProps) {
  const { message } = App.useApp();
  return (
    props.userObj ? (
      <Dropdown menu={{
        items: [
          {
            icon: <CopyOutlined />, label: `类型: ${{
              Client: '用户',
              Admin: '管理员',
            }[props.userObj.model]}`, key: 'model', hide: !props.userObj.model
          },
          { icon: <CopyOutlined />, label: `昵称: ${props.userObj.nickname || ''}`, key: 'nickname', hide: false },
          { icon: <CopyOutlined />, label: `手机: ${props.userObj.mobile || ''}`, key: 'mobile', hide: !props.userObj.mobile },
          ...(props.extraItems || []),
        ].filter(item => !item.hide),
        onClick: (item: any) => {
          const text = props.userObj[item.key] || (props.extraItems || []).find(i => i.key === item.key)?.value || ''
          if (text) {
            navigator.clipboard.writeText(text)
            message.success('复制成功')
          }

        }
      }}
        arrow
      >
        <div className="cursor-pointer">
          {props.children ? props.children : <div className="text-blue-500">{props.userObj.nickname || props.userObj.realname}</div>}
        </div>

      </Dropdown>
    ) : null

  )
}

export default UserObj
