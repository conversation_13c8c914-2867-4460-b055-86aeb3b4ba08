import { apiBillClose, apiBillGetById } from "@/apis/apis";
import UserObj from "@/components/UserObj";
import { useBillRemarkFormModal } from "@/hooks/useBillRemarkFormModal";
import { AgreeDicts } from "@/utils/agreeDict";
import { ToolsUtil } from "@/utils/tools";
import { PageContainer, ProDescriptions } from "@ant-design/pro-components";
import { App, But<PERSON>, Card, Popconfirm, Spin } from "antd";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

function BillDetail() {
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<TBill | null>(null);
  const params = new URLSearchParams(location.search);
  const id = params.get('id');
  const { message } = App.useApp();

  const { showEditModal, formModalHolder } = useBillRemarkFormModal(() => {
    fetchData();
  });

  useEffect(() => {
    if (id) {
      fetchData();
    }
  }, [id]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const res = await apiBillGetById({ id: id as string });
      setData(res);
    } catch (error) {
      console.error('获取账单详情失败', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEditRemark = () => {
    if (!data) return;
    showEditModal(data);
  };

  const handleCloseBill = async () => {
    if (!data) return;

    try {
      await apiBillClose(data.id, { reason: '手动关闭' });
      message.success('关闭成功');
      // 重新获取数据以更新界面
      fetchData();
    } catch (error) {
      console.error('关闭失败', error);
      message.error('关闭失败');
    }
  };

  return (
    <PageContainer
      title="账单详情"
      onBack={() => navigate(-1)}
      extra={[
        <Button key="edit" type="primary" onClick={handleEditRemark}>
          编辑备注
        </Button>,
        data && data.payStatus === AgreeDicts.Bill_payStatus.labelValueMap['未支付'] && (
          <Popconfirm key="close" title="确认关闭账单" description="确定要关闭此账单并退回积分吗？" onConfirm={handleCloseBill}>
            <Button key="close" type="primary" danger>
              关闭账单
            </Button>
          </Popconfirm>

        ),
      ].filter(Boolean)}
    >
      <Card>
        {loading ? (
          <div style={{ textAlign: 'center', padding: 20 }}>
            <Spin />
          </div>
        ) : data ? (
          <ProDescriptions
            column={2}
            title={false}
            dataSource={data}
            columns={[
              { title: 'ID', dataIndex: 'id' },
              { title: '订单类型', dataIndex: 'orderType', valueEnum: AgreeDicts.Bill_orderType.valueLabelMap },
              { title: '订单ID', dataIndex: 'orderId' },
              { title: '客户ID', dataIndex: 'clientUser', render: (_text, record) => <UserObj userObj={record.clientUser} /> },
              {
                title: '支付状态',
                dataIndex: 'payStatus',
                valueEnum: AgreeDicts.Bill_payStatus.valueLabelMap,
              },
              { dataIndex: 'originalMoneyCent', title: '原始金额', render: (text: number) => ToolsUtil.moneyCentFormat(text) },
              { dataIndex: 'couponDiscountCent', title: '优惠券折扣', render: (text: number) => ToolsUtil.moneyCentFormat(text) },
              { dataIndex: 'pointDiscountCent', title: '积分折扣', render: (text: number) => ToolsUtil.moneyCentFormat(text) },
              { dataIndex: 'orderMoneyCent', title: '订单金额', render: (text: number) => ToolsUtil.moneyCentFormat(text) },
              { dataIndex: 'paidMoneyCent', title: '支付金额', render: (text: number) => ToolsUtil.moneyCentFormat(text) },
              { dataIndex: 'refundedMoneyCent', title: '退款金额', render: (text: number) => ToolsUtil.moneyCentFormat(text) },
              { title: '微信支付单号', dataIndex: 'wechatPayNo' },
              { title: '支付时间', dataIndex: 'paidAt', render: (text: string) => text ? ToolsUtil.timeFormat(text) : '-' },
              { title: '创建时间', dataIndex: 'createdAt', render: (text: string) => ToolsUtil.timeFormat(text) },
              { title: '更新时间', dataIndex: 'updatedAt', render: (text: string) => ToolsUtil.timeFormat(text) },
              { title: '备注', dataIndex: 'remark', span: 2 },
            ]}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: 20 }}>未找到账单信息</div>
        )}
      </Card>
      {formModalHolder}
    </PageContainer>
  );
}

export default BillDetail;
