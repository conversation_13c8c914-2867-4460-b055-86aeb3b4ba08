
export interface TBiStatisticsNumProps {
  label: string;
  value: number | string;
}

function BiStatisticsNum(props: TBiStatisticsNumProps) {
  return (
    <div className="BiStatisticsNum relative w-[180px] h-[95px] bg-no-repeat bg-cover bg-[url('/images/bi/statistics_num_bg.webp')]">
      <div className="absolute w-full top-[25px] text-[#c9e8f2] text-center font-bold text-[28px]">{props.value}</div>
      <div className="absolute w-full top-[110px] text-[#c9e8f2] text-center text-[12px]">{props.label}</div>
    </div>
  )
}

export default BiStatisticsNum
