import NP from 'number-precision';

NP.enableBoundaryChecking(false); 

export default class NumUtils {
  /**
   * 近似，保留几位小数
   */
  static round(num?: number, decimal = 2) {
    return NP.round(num || 0, decimal);
  }

  /**
   * 让精度丢失的数字正确
   */
  static strip(num?: number) {
    return NP.strip(num || 0);
  }

  /**
   * 计算并转为字符串，用于展示数字
   **/
  static calcAndToString({
    num,
    calcFun = (num) => num,
    decimal = 2,
    nansStr = '-',
    unit = '',
  }: {
    num: unknown;
    calcFun?: (num: number) => number;
    decimal?: number;
    nansStr?: string;
    unit?: string;
  }) {
    if(typeof num === 'string' && /^-?(0|([1-9][0-9]*))(\.[\d]+)?$/.test(num)) {
      num = Number(num);
    }
    if (typeof num !== 'number') {
      return nansStr
    };
    const result = calcFun(num);
    if (typeof result !== 'number') return nansStr;
    return `${this.round(result, decimal)}${unit}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  /** 存成分的金额转字符串 */
  static moneyCentFormat(num: number, unit?: string) {
    return this.calcAndToString({
      num,
      calcFun: (num) => num / 100,
      decimal: 2,
      unit: unit === undefined ? '元' : unit,
    });
  }
}
