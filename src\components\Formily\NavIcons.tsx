import { App, Button, Form, Input, Select, Switch } from "antd";
import {
  connect,
  mapReadPretty,
  useField,
} from '@formily/react'
import { produce } from 'immer'
import { ImageUpload } from "../ImageUpload/ImageUpload";
import { useMemoizedFn } from "ahooks";

type TNavIconItem = {
  icon: string;
  label: string;
  url: string;
  appid: string;
  type: string;
  hide: boolean;
}

type NavIconsProps = {
  value: TNavIconItem[]
  onChange: (value: TNavIconItem[]) => void
};

const NavIconsEdit = ({ value, onChange }: NavIconsProps) => {


  const field = useField() as any

  const editFields = field.componentProps.editFields || ['icon', 'label', 'url', 'appid', 'type']

  const { message } = App.useApp()

  const addItem = useMemoizedFn((index) => {
    onChange(produce(value, draft => {
      draft.splice(index, 0, { icon: undefined, label: '', url: '', appid: '', type: '', hide: false })
    }))
  })

  const exchangeItem = useMemoizedFn((index, otherIndex) => {
    const item = value[index]
    const other = value[otherIndex]
    onChange(produce(value, draft => {
      draft[index] = other
      draft[otherIndex] = item
    }))
    message.success('交换成功')
  })

  const deleteItem = useMemoizedFn((index) => {
    onChange(produce(value, draft => {
      draft.splice(index, 1)
    }))
  })


  return (
    <div className="w-480px bg-gray-200 p-4 rounded-md flex flex-col gap-4">
      {value.map((item, index) => (
        <div key={index} className="bg-white p-4 rounded-md">
          <Form labelCol={{ span: 4 }}>
            <Form.Item label="序号">
              <div>{index + 1}</div>
            </Form.Item>
            {editFields.includes('icon') && (
              <Form.Item label="图标">
                <ImageUpload
                  value={[item.icon]}
                  onChange={(imagesList) => {
                    onChange(produce(value, draft => {
                      draft[index].icon = imagesList[0]
                    }))
                  }}
                  maxCount={1}
                />
              </Form.Item>
            )}
            {editFields.includes('label') && (
              <Form.Item label="名称">
                <Input value={item.label} onChange={e => onChange(produce(value, draft => {
                  draft[index].label = e.target.value
                }))} />
              </Form.Item>
            )}
            {editFields.includes('type') && field.componentProps.typeOptions && field.componentProps.typeOptions.length > 0 && (
              <Form.Item label="类型">
                <Select value={item.type} options={field.componentProps.typeOptions || []} onChange={v => onChange(produce(value, draft => {
                  draft[index].type = v
                }))} />
              </Form.Item>
            )}
            {editFields.includes('url') && (
              <Form.Item label="链接">
                <Input value={item.url} onChange={e => onChange(produce(value, draft => {
                  draft[index].url = e.target.value
                }))} />
              </Form.Item>
            )}
            {editFields.includes('appid') && (
              <Form.Item label="appid">
                <Input value={item.appid} onChange={e => onChange(produce(value, draft => {
                  draft[index].appid = e.target.value
                }))} />
              </Form.Item>
            )}
            {editFields.includes('hide') && (
              <Form.Item label="显示">
                <Switch value={!item.hide} onChange={v => onChange(produce(value, draft => {
                  draft[index].hide = !v
                }))} />
              </Form.Item>
            )}
            <div className="flex justify-center gap-2">
              <Button onClick={() => addItem(index)}>上方增加</Button>
              <Button onClick={() => exchangeItem(index, index - 1)} disabled={index === 0}>向上移动</Button>
              <Button onClick={() => exchangeItem(index, index + 1)} disabled={index === value.length - 1}>向下移动</Button>
              <Button danger onClick={() => deleteItem(index)}>删除</Button>
            </div>

          </Form>
        </div>
      ))}
      <Button className="w-full" onClick={() => addItem(value.length)}>增加一条</Button>
    </div>
  );
}

const NavIconsPreview = ({ value }: NavIconsProps) => {
  const field = useField() as any;
  const cols = field.componentProps.cols || 4
  return (
    <div className="bg-gray-200 p-4 rounded-md">
      <div className="rounded-md bg-white flex flex-wrap gap-8px p-8px" >
        {(value || []).filter(item => !item.hide).map((item, index) => (
          <div key={index} className="flex flex-col items-center pt-8px" style={{
            width: `calc((100% - ${cols - 1} * 8px) / ${cols})`
          }} >
            <img className="w-32px h-32px" src={item.icon} />
            <div>{item.label}</div>
          </div>
        ))}
      </div>
    </div>

  )
}

export default connect(
  NavIconsEdit,
  mapReadPretty(NavIconsPreview),
);
