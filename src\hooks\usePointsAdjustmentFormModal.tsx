import { apiPointsRecordAdd } from '@/apis/apis';
import { useFormModal } from '@/components/FormilyModal';
import { useMemoizedFn } from 'ahooks';
import { App } from 'antd';

interface TPointsAdjustmentFormData {
  pointsChange: number;
  description?: string;
}

export const usePointsAdjustmentFormModal = (cb: () => void) => {
  const { message } = App.useApp();
  const { formModalRef, formModalHolder } = useFormModal<TPointsAdjustmentFormData>();

  const showAdjustModal = useMemoizedFn(async (clientUser: TClientUser) => {
    formModalRef.current?.show({
      modalTitle: `调整用户积分 - ${clientUser.nickname}`,
      initialValues: {
        pointsChange: 0,
        description: '',
      },
      modalWidth: 600,
      onAutoSubmit: async (values: TPointsAdjustmentFormData) => {
        // 扣减积分时检查余额
        if (values.pointsChange < 0 && clientUser.points + values.pointsChange < 0) {
          message.error(`积分不足，当前积分：${clientUser.points}，扣减后将为负数`);
          throw new Error('积分不足');
        }

        await apiPointsRecordAdd({
          clientUserId: clientUser.id,
          pointsChange: values.pointsChange,
          description: values.description,
        });

        message.success("积分调整成功");
        cb();
      },
      schema: {
        type: "object",
        properties: {
          pointsChange: {
            title: "积分变化",
            type: "number",
            required: true,
            "x-decorator": "FormItem",
            "x-component": "NumberPicker",
            "x-component-props": {
              precision: 0,
              placeholder: "正数为增加，负数为扣减",
              style: { width: '100%' }
            },
            "x-decorator-props": {
              tooltip: "正数为增加积分，负数为扣减积分"
            }
          },
          description: {
            title: "描述",
            type: "string",
            "x-decorator": "FormItem",
            "x-component": "Input.TextArea",
            required: true,
            "x-component-props": {
              rows: 3,
              placeholder: "请输入调整原因（可选）"
            }
          },
        },
      },
    });
  });

  return {
    showAdjustModal,
    formModalHolder,
  };
};
