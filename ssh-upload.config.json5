{
  // 远程服务器配置
  remote: [
    {
      name: "prod",
      host: "*********",
      port: 22,
      username: "root",
      // 远程目录，可以使用 ~ 表示用户目录
      directory: "/www/admin-prod.flowerprice.top",
      // 标记配置是否已完成
      configComplete: true,
    },
    {
      name: "dev",
      host: "*********",
      port: 22,
      username: "root",
      // 远程目录，可以使用 ~ 表示用户目录
      directory: "/www/admin-dev.flowerprice.top",
      // 标记配置是否已完成
      configComplete: true,
    },
  ],
  // 本地目录配置
  local: {
    // 本地目录，可以使用 {HOME} 表示用户主目录
    directory: "./dist",
    // 排除的文件或目录
    exclude: [".DS_Store", ".git", "node_modules", "*.log"],
  },
}
