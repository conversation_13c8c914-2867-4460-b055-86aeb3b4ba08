import { apiDashboardTricycle } from "@/apis/apis.api";
import { useEcharts } from "@/hooks/useEcharts";
import { ProCard } from "@ant-design/pro-components";
import { useRequest } from "ahooks";
import dayjs from "dayjs";
import { useEffect } from "react";

type TricycleChartProps = {

};

export const TricycleChart = ({ }: TricycleChartProps) => {

  const { data } = useRequest(async () => {
    const res = await apiDashboardTricycle({
      dateRange: [
        dayjs().subtract(30, 'days').startOf('day').valueOf(),
        dayjs().add(1, 'days').startOf('day').valueOf(),
      ]
    })
    return res
  }, {
    pollingInterval: 60 * 1000,
  })

  const { chart, setContainerRef } = useEcharts({
    theme: 'light',
  });

  useEffect(() => {
    if (chart && data) {
      const xAxisData = data.map((item) => item.date);
      const y1AxisData = data.map((item) => item.add);
      const y2AxisData = data.map((item) => item.total);

      chart.setOption({
        grid: {
          left: 40,
          right: 40,
          bottom: 40,
          top: 30,

        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData
        },
        yAxis: [
          {
            name: '新增数',
            type: 'value',
            alignTicks: true,
            nameTextStyle: {
              color: '#15bf1d',
            },
          },
          {
            name: '总数',
            type: 'value',
            alignTicks: true,
            nameTextStyle: {
              color: '#FFB92C',
            },
          }
        ],
        series: [
          {
            name: '新增数',
            type: 'bar',
            yAxisIndex: 0,
            color: '#FFB92C',
            // barWidth: 18,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [{
                  offset: 0, color: '#b3edb6'
                }, {
                  offset: 1, color: '#80cc83'
                }],
              }
            },
            data: y1AxisData,
          },
          {
            name: '总数',
            type: 'line',
            yAxisIndex: 1,
            color: '#FFB92C',
            lineStyle: {
              color: '#FFB92C',
            },
            data: y2AxisData,
          },
        ]
      })
    }
  }, [chart, data])

  return (
    <ProCard title="三轮车新增趋势" bordered className="flex-1">
      <div className="h-300px" ref={setContainerRef}>
      </div>
    </ProCard>
  );
}
