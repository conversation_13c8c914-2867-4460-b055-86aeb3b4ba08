import { apiDashboardLcPark } from "@/apis/apis.api";
import { useEcharts } from "@/hooks/useEcharts";
import { useRequest } from "ahooks"
import dayjs from "dayjs";
import { useEffect } from "react";

const Bi01ParkCount = () => {
  
    const { data } = useRequest(async () => {
        const res = await apiDashboardLcPark({
            dateRange: [
                dayjs().subtract(60, 'minutes').valueOf(),
                dayjs().valueOf(),
            ]
        })
        return res
    }, {
        pollingInterval: 60 * 1000,
    })


    const { chart, setContainerRef } = useEcharts({
        theme: 'walden',
    });

    useEffect(() => {
        if (chart && data) {
            const xData = data.map((item) => dayjs(item.minutes * 1000 * 60).format('HH:mm'));
            const yData = data.map((item) => item.inParkCount);

            chart.setOption({
                grid: {
                    left: 50,
                    right: 50,
                    bottom: 20,
                    top: 40,
                },
                tooltip: {
                    trigger: 'axis',
                },
                xAxis: {
                    name: '时间\n(时分)',
                    nameTextStyle: {
                        color: '#02CABB',
                        align: 'left',
                    },
                    type: 'category',
                    data: xData,
                    axisTick: {
                        show: false,
                        interval: 4,
                    },
                    axisLabel: {
                        interval: 4,
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#C9E8F2'
                        }
                    },
                },
                yAxis: {
                    name: '车辆(辆)',
                    nameTextStyle: {
                        color: '#02CABB',
                    },
                    type: 'value',
                },
                series: [
                    {
                        name: '停车数量',
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 8,
                        itemStyle: {
                            color: '#3CB371',
                        },
                        lineStyle: {
                            width: 2,
                            color: '#3CB371',
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0,
                                    color: '#3CB37188'
                                }, {
                                    offset: 1,
                                    color: '#3CB37100'
                                }]
                            }
                        },
                        data: yData,
                    }
                ]
            });
        }
    }, [chart, data]);

    return <div ref={setContainerRef} className="w-full h-full"></div>;
}

export default Bi01ParkCount;