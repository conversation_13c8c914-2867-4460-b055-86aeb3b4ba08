import { apiClientUserPageList } from "@/apis/apis";
import { ActionType, ProTable } from "@ant-design/pro-components";
import { Space } from "antd";
import { useRef } from "react";
import { useNavigate } from "react-router-dom";

function ClientUserList() {
  const tableRef = useRef<ActionType>();
  const navigate = useNavigate();

  return (
    <div>
      <ProTable<TClientUser>
        headerTitle="客户列表"
        actionRef={tableRef}
        rowKey="id"
        search={{
          defaultCollapsed: false,
        }}
        pagination={{
          pageSize: 20,
          showQuickJumper: true,
        }}
        columns={[
          { dataIndex: 'id', title: 'ID', search: true, width: 180 },
          { dataIndex: 'nickname', title: '昵称', search: true },
          { dataIndex: 'mobile', title: '手机号', search: true },
          { dataIndex: 'email', title: '邮箱', search: true },
          { dataIndex: 'wxOpenid', title: '微信OpenID', search: true },
          { 
            dataIndex: 'isVip', 
            title: 'VIP状态', 
            search: true,
            valueType: 'select',
            valueEnum: {
              true: { text: '是', status: 'Success' },
              false: { text: '否', status: 'Default' },
            },
            render: (_, record) => (
              <span>{record.isVip ? '是' : '否'}</span>
            )
          },
          { dataIndex: 'vipEndAt', title: 'VIP至', valueType: 'dateTime', search: false },
          {
            dataIndex: 'action', 
            title: '操作', 
            search: false,  
            width: 120, 
            render: (_, record) => (
              <Space>
                <a onClick={() => navigate(`/clientUserDetail?id=${record.id}`)}>查看</a>
              </Space>
            )
          },
        ]}
        request={async (params) => {
          const res = await apiClientUserPageList({
            pageIndex: params.current - 1,
            pageSize: params.pageSize,
            id: params.id,
            nickname: params.nickname,
            mobile: params.mobile,
            email: params.email,
            wxOpenid: params.wxOpenid,
            isVip: params.isVip === 'true' ? true : (params.isVip === 'false' ? false : undefined),
            keyword: params.keyword,
          });
          return {
            data: res.rows,
            total: res.totalRowCount,
            success: true,
          };
        }}
      />
    </div>
  );
}

export default ClientUserList;
