import LiveVideo, { LiveVideoRef } from '../LiveVideo';
import { useCreation } from 'ahooks';
import { useRef } from 'react';
import { Carousel } from "antd";

export interface TBiVideoMonitorProps {
  videoSources: string[],
  itemWidth?: number,
  itemHeight?: number,
  autoplaySpeed?: number,
}

function BiVideoMonitor(props: TBiVideoMonitorProps) {
  const {
    itemWidth = 576,
    itemHeight = 324,
    autoplaySpeed = 10000,
    videoSources,
  } = useCreation(() => props || {} as TBiVideoMonitorProps, [props]);

  const videoRefs = useRef<LiveVideoRef[]>([]);

  return (
    <div className="relative">
      <Carousel
        autoplay
        autoplaySpeed={autoplaySpeed}
        afterChange={(index) => {
          videoRefs.current.forEach((videoRef, i) => {
            if (i === index) {
              videoRef.play();
            } else {
              videoRef.pause();
            }
          });
        }}
      >
        {
          videoSources.map((item, index) => {
            return (
              <div key={index} style={{ width: itemWidth, height: itemHeight, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <LiveVideo
                  ref={el => (videoRefs.current[index] = el)}
                  src={item}
                  className="w-full h-full object-cover rounded-xl bg-black"
                  width={itemWidth}
                  height={itemHeight}
                  autoPlay
                  muted
                  onError={(error) => console.error('Video error:', error)}
                />
              </div>
            )
          })
        }
      </Carousel>
      
    </div>
  );
}

export default BiVideoMonitor
