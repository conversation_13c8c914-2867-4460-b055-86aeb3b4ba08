interface TAdminUser {
  id: string;
  username: string;
  nickname: string;
  mobile?: string;
  email?: string;
}

interface TAdminLoginReq {
  username: string;
  password: string;
}

/**
 * 登录响应
 */
interface TAdminLoginRes {
  /** 用户ID */
  user: TAdminUser;
  /** JWT令牌 */
  token: string;
  /** 过期时间 */
  expiredAt: string;
}

interface TBill {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  /** ENUM:
    VIP_FEE -  会员费
    WITHDRAW -  提现
 */
  orderType: string;
  orderId: string;
  clientUserId: string;
  clientUser: TClientUser;
  originalMoneyCent?: number;
  couponDiscountCent?: number;
  pointDiscountCent?: number;
  orderMoneyCent: number;
  paidMoneyCent?: number;
  refundedMoneyCent?: number;
  paidAt?: string;
  payStatus: string;
  wechatPayNo?: string;
  payFeeCent?: number;
  remark?: string;
  closedAt?: string;
  closedReason?: string;
}

interface TBillPage {
  rows: TBill[];
  totalPageCount: number;
  totalRowCount: number;
}

interface TBillUpdateReq {
  id: string;
  remark?: string;
}

/**
 * 客户消息
 */
interface TClientMessage {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  /** 标题 */
  title: string;
  /** 内容 */
  content: string;
  /** 消息类型
ENUM:SYSTEM-系统消息,PAYMENT-支付相关,VIP-VIP相关,PROMOTION-推广活动,NOTIFICATION-通知消息,FEEDBACK-反馈回复
 */
  messageType: string;
  clientUserId: string;
  clientUser: TClientUser;
  /** 关联实体ID */
  relatedEntityId?: string;
  /** 阅读时间 */
  readAt?: string;
  /** 是否已读 */
  isRead: boolean;
}

interface TAdminMessagePageListReq {
  pageIndex: number;
  pageSize: number;
  clientUserId?: string;
  clientUserKeyword?: string;
  messageType?: string;
  isRead?: boolean;
  createdAtStart?: string;
  createdAtEnd?: string;
}

interface TClientMessagePage {
  rows: TClientMessage[];
  totalPageCount: number;
  totalRowCount: number;
}

interface TClientUser {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  nickname: string;
  avatar: string;
  mobile?: string;
  email?: string;
  wxOpenid?: string;
  latestLoginAt?: string;
  vipEndAt?: string;
  /** 积分 */
  points: number;
  isVip: boolean;
  roles: string[];
  fromUserId?: string;
  wxShareQrCode?: string;
  /** 自动使用积分 */
  autoUsePoints: boolean;
}

interface TClientUserPage {
  rows: TClientUser[];
  totalPageCount: number;
  totalRowCount: number;
}

interface TFeedbackPage {
  rows: TFeedback[];
  totalPageCount: number;
  totalRowCount: number;
}

interface TFeedbackProcessReq {
  id: string;
  reply?: string;
  remark?: string;
  status: string;
}

/**
 * 用户反馈实体
 */
interface TFeedback {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  clientUserId: string;
  clientUser: TClientUser;
  mobile: string;
  reply?: string;
  remark?: string;
  content: string;
  /** 处理状态
ENUM:PENDING-待处理,PROCESSED-已处理
 */
  status: string;
  /** 附件URL数组 */
  attachments: string[];
}

/**
 * 管理员为用户添加点数请求DTO
 */
interface TAdminAddPointsReq {
  /** 用户ID */
  clientUserId: string;
  /** 点数变化(正数为增加，负数为减少) */
  pointsChange: number;
  /** 描述 */
  description?: string;
}

/**
 * 客户点数变化记录
 */
interface TClientUserPointsRecord {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  clientUserId: string;
  clientUser: TClientUser;
  /** 点数变化(正数为增加，负数为减少) */
  pointsChange: number;
  /** 记录类型
ENUM:REFERRAL_COMMISSION-推荐返佣,MANUAL_ADJUSTMENT-手动调整,CONSUMPTION-消费扣减,REFUND-退款返还
 */
  recordType: string;
  /** 描述 */
  description?: string;
  /** 关联账单ID */
  relatedBillId?: string;
}

interface TClientUserPointsRecordPage {
  rows: TClientUserPointsRecord[];
  totalPageCount: number;
  totalRowCount: number;
}

interface TVipFeeOrder {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  vipFeeTypeId: string;
  vipFeeTypeName: string;
  vipFeeTypeDays: number;
  startTime?: string;
  endTime?: string;
  amountCent: number;
  clientUserId: string;
  clientUser: TClientUser;
  billId?: string;
  bill?: TBill;
  autoCloseTime?: string;
  /** ENUM:
WAIT_PAY - 待支付
PAID - 已支付
CLOSED - 已关闭
 */
  status: string;
}

interface TVipFeeOrderPage {
  rows: TVipFeeOrder[];
  totalPageCount: number;
  totalRowCount: number;
}

interface TVipFeeTypeCreateReq {
  name: string;
  days: number;
  amountCent: number;
  isEnabled: boolean;
  isPublic: boolean;
}

interface TVipFeeType {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  name: string;
  days: number;
  amountCent: number;
  isEnabled: boolean;
  isPublic: boolean;
}

interface TVipFeeTypePage {
  rows: TVipFeeType[];
  totalPageCount: number;
  totalRowCount: number;
}

interface TVipFeeTypeUpdateReq {
  id: string;
  name?: string;
  days?: number;
  amountCent?: number;
  isEnabled?: boolean;
  isPublic?: boolean;
}

/**
 * 客户端用户设置请求DTO
 */
interface TClientUserSettingsReq {
  /** 昵称 */
  nickname?: string;
  /** 头像URL */
  avatar?: string;
  /** 邮箱 */
  email?: string;
  /** 是否自动使用积分 */
  autoUsePoints?: boolean;
}

/**
 * 客户端用户设置响应DTO
 */
interface TClientUserSettingsRes {
  id: string;
  nickname: string;
  avatar: string;
  email?: string;
  autoUsePoints: boolean;
  points: number;
  isVip: boolean;
}

/**
 * 微信小程序登录请求
 */
interface TWxLoginReq {
  /** 微信小程序登录时获取的code */
  code: string;
  fromUserId?: string;
}

/**
 * 登录响应
 */
interface TClientLoginRes {
  /** 用户ID */
  user: TClientUser;
  /** JWT令牌 */
  token: string;
  /** 过期时间 */
  expiredAt: string;
  /** 是否是新用户 */
  isNewUser: boolean;
}

interface TFavoriteCheckReq {
  flowerIds: string[];
}

interface TFlowerPage {
  rows: TFlower[];
  totalPageCount: number;
  totalRowCount: number;
}

interface TMyFeedbackListReq {
  pageIndex: number;
  pageSize: number;
  status?: string;
}

interface TFeedbackSubmitReq {
  mobile: string;
  content: string;
  attachments: string[];
}

interface TFlowerChart {
  name: string;
  price: number;
  date: string;
}

interface TFlowerPrice {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  flowerId: string;
  price: number;
  grade?: string;
  batchTime: string;
  isZip: boolean;
  flower: TFlower;
}

/**
 * 鲜花
 */
interface TFlower {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  /** 鲜花名称 */
  name: string;
  parentId?: string;
  parent?: TFlower;
  /** 最近的行情ID */
  latestFlowerMarketId?: string;
  /** 最近的行情 */
  latestFlowerMarket?: TFlowerMarket;
}

/**
 * 标记消息已读请求DTO
 */
interface TClientMessageMarkReadReq {
  /** 消息ID列表 */
  messageIds: string[];
}

/**
 * 客户消息分页查询请求DTO
 */
interface TClientMessagePageListReq {
  pageIndex: number;
  pageSize: number;
  messageType?: string;
  isRead?: boolean;
}

interface TWxPayParams {
  orderId?: string;
  billId?: string;
  /** 是否已支付
一般是0元订单，自动支付，不再调用微信支付
 */
  paid?: boolean;
  /** 小程序 AppId */
  appId?: string;
  /** 时间戳
单位：秒
 */
  timeStamp?: string;
  /** 随机字符串 */
  nonceStr?: string;
  /** 固定值 */
  packAge?: string;
  /** 签名类型 */
  signType?: string;
  /** 签名
这里用的 MD5/RSA 签名
 */
  paySign?: string;
}

interface TBaseIdReq {
  id: string;
}

/**
 * API统一响应格式
 */
interface TApiResponse_String {
  /** 响应码，0表示成功 */
  code: number;
  /** 响应消息 */
  message: string;
  /** 响应数据 */
  data?: string;
}

interface TFlowerMarket {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  flowerId: string;
  strength: number;
  price: number;
  day1Price: number;
  day1DiffPercent: number;
  day7Price: number;
  day7DiffPercent: number;
  day30Price: number;
  day30DiffPercent: number;
  batchTime: string;
  isZip: boolean;
  day7DiffSort: number;
  flower: TFlower;
}

