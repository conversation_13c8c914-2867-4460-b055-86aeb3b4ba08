import { App, Button, Form, Input, Select } from "antd";
import {
  connect,
  mapReadPretty,
  useField,
} from '@formily/react'
import { produce } from 'immer'
import { ImageUpload } from "../ImageUpload/ImageUpload";
import { useMemoizedFn } from "ahooks";
import { AgreeDicts } from "@/utils/agreeDict";

type TNavIconItem = {
  icon: string;
  label: string;
  type: string;
}

type StoreNavIconsProps = {
  value: TNavIconItem[]
  onChange: (value: TNavIconItem[]) => void
};

const StoreNavIconsEdit = ({ value, onChange }: StoreNavIconsProps) => {

  const { message } = App.useApp()

  const addItem = useMemoizedFn((index) => {
    onChange(produce(value, draft => {
      draft.splice(index, 0, { icon: undefined, label: '', type: '' })
    }))
  })

  const exchangeItem = useMemoizedFn((index, otherIndex) => {
    const item = value[index]
    const other = value[otherIndex]
    onChange(produce(value, draft => {
      draft[index] = other
      draft[otherIndex] = item
    }))
    message.success('交换成功')
  })

  const deleteItem = useMemoizedFn((index) => {
    onChange(produce(value, draft => {
      draft.splice(index, 1)
    }))
  })


  return (
    <div className="w-480px bg-gray-200 p-4 rounded-md flex flex-col gap-4">
      {value.map((item, index) => (
        <div key={index} className="bg-white p-4 rounded-md">
          <Form labelCol={{ span: 4 }}>
            <Form.Item label="图标">
              <ImageUpload
                value={[item.icon]}
                onChange={(imagesList) => {
                  onChange(produce(value, draft => {
                    draft[index].icon = imagesList[0]
                  }))
                }}
                maxCount={1}
              />
            </Form.Item>
            <Form.Item label="名称">
              <Input value={item.label} onChange={e => onChange(produce(value, draft => {
                draft[index].label = e.target.value
              }))} />
            </Form.Item>
            <Form.Item label="类型">
              <Select options={AgreeDicts.Product_type.options} value={item.type} onChange={e => onChange(produce(value, draft => {
                draft[index].type = e
              }))} />
            </Form.Item>
            <div className="flex justify-center gap-2">
              <Button onClick={() => addItem(index)}>上方增加</Button>
              <Button onClick={() => exchangeItem(index, index - 1)} disabled={index === 0}>向上移动</Button>
              <Button onClick={() => exchangeItem(index, index + 1)} disabled={index === value.length - 1}>向下移动</Button>
              <Button danger onClick={() => deleteItem(index)}>删除</Button>
            </div>
          </Form>
        </div>
      ))}
      <Button className="w-full" onClick={() => addItem(value.length)}>增加一条</Button>
    </div>
  );
}

const StoreNavIconsPreview = ({ value }: StoreNavIconsProps) => {
  const field = useField() as any;
  const cols = field.componentProps.cols || 4
  return (
    <div className="bg-gray-200 p-4 rounded-md">
      <div className="rounded-md bg-white flex flex-wrap gap-8px p-8px" >
        {(value || []).map((item, index) => (
          <div key={index} className="flex flex-col items-center pt-8px" style={{
            width: `calc((100% - ${cols - 1} * 8px) / ${cols})`
          }} >
            <img className="w-32px h-32px" src={item.icon} />
            <div>{item.label}</div>
          </div>
        ))}
      </div>
    </div>

  )
}

export default connect(
  StoreNavIconsEdit,
  mapReadPretty(StoreNavIconsPreview),
);
