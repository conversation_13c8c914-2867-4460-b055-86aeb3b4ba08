import { apiDashboardPlaySource } from "@/apis/apis.api";
import { useEcharts } from "@/hooks/useEcharts";
import { ProCard } from "@ant-design/pro-components";
import { useRequest } from "ahooks";
import { Radio } from "antd";
import dayjs from "dayjs";
import groupBy from "lodash/groupBy";
import { useEffect, useState } from "react";

const hourFormat = 'MM月DD日 HH时'
const dayFormat = 'MM月DD日'

export const PlaySourceChart = () => {
  const [timeUnit, setTimeUnit] = useState<'h' | 'd'>('h');
  const [dates, setDates] = useState<string[]>([]);

  const { data } = useRequest(async () => {
    const startDate = dayjs().startOf(timeUnit).subtract(24, timeUnit);
    const endDate = dayjs().add(1, timeUnit).startOf(timeUnit);
    const newDates: string[] = []
    const timeFormat = timeUnit === 'h' ? hourFormat : dayFormat
    for (let i = 0; i < 25; i++) {
      newDates.push(startDate.clone().add(i, timeUnit).format(timeFormat))
    }
    const res = await apiDashboardPlaySource({
      startTime: startDate.format('YYYY-MM-DD HH:mm:ss'),
      endTime: endDate.format('YYYY-MM-DD HH:mm:ss'),
      timeUnit
    })
    setDates(newDates)
    console.log(newDates)
    return res
  }, {
    pollingInterval: 60 * 1000,
    refreshDeps: [timeUnit],
  })

  const { chart, setContainerRef } = useEcharts({
    theme: 'light',
  });

  useEffect(() => {
    if (chart && data) {
      const groupByKey = groupBy(data, 'key');
      const keys = Object.keys(groupByKey);
      const series = keys.map((key) => {
        const timeFormat = (data[0]?.timeUnit || timeUnit) === 'h' ? hourFormat : dayFormat
        const timeMap = (groupByKey[key] || []).reduce((acc, item) => {
          acc[dayjs(item.time).format(timeFormat)] = item.count
          return acc
        }, {} as Record<string, number>)
        return {
          name: key,
          type: 'line',
          // yAxisIndex: index,
          // color: '#FFB92C',
          // lineStyle: {
          //   color: '#FFB92C',
          // },
          data: dates.map(date => timeMap[date] || 0),
        }
      })


      const xAxisData = dates;
      // const y1AxisData = data.map((item) => item.add);
      // const y2AxisData = data.map((item) => item.total);

      chart.setOption({
        grid: {
          left: 40,
          right: 40,
          bottom: 40,
          top: 30,

        },
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          top: 0,
          data: keys,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData,
          
        },
        yAxis: {
          name: '点击数',
          type: 'value',
          nameTextStyle: {
            color: '#FFB92C',
          },
        },
        series,
      })
    }
  }, [chart, data, dates])

  return (
    <ProCard title="吃喝玩乐来源" bordered className="flex-1" extra={
      <Radio.Group  optionType="button" value={timeUnit} onChange={e => setTimeUnit(e.target.value)} options={[
        { label: '按天', value: 'd' },
        { label: '按小时', value: 'h' },
      ]}></Radio.Group>
    }>
      <div className="h-300px" ref={setContainerRef}>
      </div>
    </ProCard>
  );
}
