import { apiBiPeopleCountInAndOutReport } from "@/apis/apis.api";
import { useEcharts } from "@/hooks/useEcharts";
import { useStepedRequest } from "@/hooks/useStepedRequest";
import { settingModel } from "@/stores/settingModel";
import { useCreation, useRequest } from "ahooks"
import dayjs from "dayjs";
import { useModel } from "foca";
import { useEffect } from "react";

const hours = ['00-01', '02-03', '04-05', '06-07', '08-09', '10-11', '12-13', '14-15', '16-17', '18-19', '20-21', '22-23'];
const days = [ '周一', '周二', '周三', '周四', '周五', '周六','周日',].reverse();

const Bi01WeekHeatmap = () => {
    
    const settingModelState = useModel(settingModel);
    const peopleCountRate = useCreation(() => settingModelState?.Admin?.peopleCountRate || 1, [settingModelState?.Admin?.peopleCountRate]);
    // refreshDeps: [peopleCountRate]

    const apiBiPeopleCountInAndOutReportSteped = useStepedRequest(apiBiPeopleCountInAndOutReport);


    const { data } = useRequest(async () => {
        const start = dayjs().startOf('day').subtract(8, 'day').format('YYYY-MM-DD HH');
        const end = dayjs().startOf('day').subtract(1, 'day').format('YYYY-MM-DD HH');
        const res = await apiBiPeopleCountInAndOutReportSteped({
            timeUnit: 'hour',
            start: start,
            end: end
        })
        const list = []
        for (let i = 1; i < res.in.length; i++) {
            const day = (dayjs(res.in[i].time).day() + 1) % 7;
            for (let j = 1; j < res.in[i].data.length; j += 2) {
                if (j % 2 === 1) {
                    const hour = Math.floor((j - 1) / 2)
                    const count = (res.in[i].data[j - 1].remainder || 0) + (res.in[i].data[j].remainder || 0)
                    list.push([
                        hour,
                        day,
                        Math.round(count * peopleCountRate),
                    ])
                }
            }
        }

        list.sort((a, b) => {
            return  b[1] - a[1]
        })

        return list
    }, {
        pollingInterval: 60 * 60 * 1000,
        refreshDeps: [peopleCountRate],
    })

    const { chart, setContainerRef } = useEcharts({
        theme: 'walden',
    });

    useEffect(() => {
        if (chart && data) {
            let { min, max } = data.reduce((pre, cur) => {
                return {
                    min: Math.min(pre.min, cur[2]),
                    max: Math.max(pre.max, cur[2])
                }
            }, { min: 9999, max: 0 })
            chart.setOption({
                grid: {
                    left: 60,
                    right: 60,
                    bottom: 50,
                    top: 35,
                },
                tooltip: {
                    position: 'top',
                    formatter: function (params) {
                        return `${days[params.value[1]]} ${hours[params.value[0]]}时<br/>入场人数：${params.value[2]}人`;
                    }
                },
                xAxis: {
                    name: '时间段\n(时)',
                    nameTextStyle: {
                        color: '#02CABB',
                        align: 'left',
                    },
                    type: 'category',
                    data: hours,
                    splitArea: {
                        show: true
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#C9E8F2'
                        }
                    },
                    axisLabel: {
                        formatter: '{value}',
                        fontSize: 10
                    }
                },
                yAxis: {
                    name: '星期',
                    nameTextStyle: {
                        color: '#02CABB',
                        align: 'right',
                    },
                    type: 'category',
                    data: days,
                    splitArea: {
                        show: true
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#C9E8F2'
                        }
                    }
                },
                visualMap: {
                    min,
                    max,
                    calculable: true,
                    orient: 'horizontal',
                    left: 'center',
                    bottom: -5,
                    itemWidth: 8,
                    itemHeight: 200,
                    inRange: {
                        color: [
                            '#44B6FF',
                            // '#61DF5A',
                            // '#DFCD5A',
                            '#DF5A5A',
                        ]
                    },
                    textStyle: {
                        color: '#C9E8F2'
                    }
                },
                series: [{
                    name: '入场人数',
                    type: 'heatmap',
                    data: data,
                    label: {
                        show: true,
                        color: '#fff',
                        fontSize: 8
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            });
        }
    }, [chart, data]);

    return <div ref={setContainerRef} className="w-full h-full"></div>;
}

export default Bi01WeekHeatmap;
