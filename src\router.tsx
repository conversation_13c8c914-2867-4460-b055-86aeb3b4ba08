import { nanoid } from "nanoid";
import { createBrowserRouter, Navigate } from "react-router-dom";
import {
  AreaChartOutlined,
  CommentOutlined,
  UserOutlined,
  CreditCardOutlined,
  CrownOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import DefaultLayout from './layouts/DefaultLayout';
import LoginPage from '@/pages/login';
import HomePage from '@/pages/home';
import FeedbackList from '@/pages/feedbackList.tsx';
import KeepAlive from "react-activation";
import Dashboard from '@/pages/dashboard.tsx';
import ClientUserList from '@/pages/clientUserList';
import ClientUserDetail from '@/pages/clientUserDetail';
import BillList from '@/pages/billList';
import BillDetail from '@/pages/billDetail';
import VipFeeTypeList from '@/pages/vipFeeTypeList';
import VipFeeTypeDetail from '@/pages/vipFeeTypeDetail';
import VipFeeOrderList from '@/pages/vipFeeOrderList';
import VipFeeOrderDetail from '@/pages/vipFeeOrderDetail';
//__gen1__;

/**
 * 
 */
export type TLayoutRoute = {
  name: string;
  path: string;
  component?: JSX.Element;
  redirect?: string;
  icon?: JSX.Element;
  routes?: TLayoutRoute[];
  hideInMenu?: boolean;
  hideInRouter?: boolean;
  keepAlive?: boolean;
};

/**
 * 用于layout的菜单路由
 */
export const layoutRoute: TLayoutRoute = {
  path: '',
  name: '',
  routes: [
    {
      path: '/dashboard',
      name: '仪表盘',
      icon: <AreaChartOutlined />,
      component: <Dashboard />,
    },
    {
      path: '/feedbackList',
      name: '投诉建议',
      icon: <CommentOutlined />,
      component: <FeedbackList />,
      keepAlive: true,
    },
    {
      path: '/clientUserList',
      name: '客户管理',
      icon: <UserOutlined />,
      component: <ClientUserList />,
      keepAlive: true,
    },
    {
      path: '/clientUserDetail',
      name: '客户详情',
      hideInMenu: true,
      component: <ClientUserDetail />,
    },
    {
      path: '/billList',
      name: '账单管理',
      icon: <CreditCardOutlined />,
      component: <BillList />,
      keepAlive: true,
    },
    {
      path: '/billDetail',
      name: '账单详情',
      hideInMenu: true,
      component: <BillDetail />,
    },
    {
      path: '/vipFeeTypeList',
      name: '会员费类型',
      icon: <CrownOutlined />,
      component: <VipFeeTypeList />,
      keepAlive: true,
    },
    {
      path: '/vipFeeTypeDetail',
      name: '会员费类型详情',
      hideInMenu: true,
      component: <VipFeeTypeDetail />,
    },
    {
      path: '/vipFeeOrderList',
      name: '会员费订单',
      icon: <ShoppingCartOutlined />,
      component: <VipFeeOrderList />,
      keepAlive: true,
    },
    {
      path: '/vipFeeOrderDetail',
      name: '会员费订单详情',
      hideInMenu: true,
      component: <VipFeeOrderDetail />,
    },
    //__gen2__;
  ],
};

export const keepAlivePathSet = new Set();

/**
 * 递归地将layoutRoute转换为router所需的RouteObject
 */
const mapRouteObject = (routes: TLayoutRoute[]) => {
  const result: {
    path: string;
    element: JSX.Element;
  }[] = [];
  routes.forEach(item => {
    if (item.component) {
      if (item.keepAlive) {
        keepAlivePathSet.add(item.path);
      }
      result.push({
        path: item.path,
        element: item.keepAlive ? (
          <KeepAlive id={nanoid()} name={item.path}>
            {item.component}
          </KeepAlive>
        ) : item.component,
      });
    } else if (item.redirect) {
      result.push({ path: item.path, element: <Navigate to={item.redirect} replace={true} />, });
    }
    if (item.routes) {
      result.push(...mapRouteObject(item.routes));
    }
  });
  return result;
};

const router = createBrowserRouter([
  {
    path: "/login",
    element: <LoginPage />,
  },
  {
    path: '/',
    element: <HomePage />,
  },
  {
    path: "",
    element: <DefaultLayout />,
    children: mapRouteObject([layoutRoute]),
  },
]);

/**
 * 递归地将layoutRoute转换为[path, name][]
 */
const mapPathInfo = (routes: TLayoutRoute[], breadcrumb: {
  path?: string;
  title: string;
}[] = []) => {
  const result: [string, {
    title: string;
    breadcrumb: {
      path?: string;
      title: string;
    }[]
  }][] = [];
  routes.forEach(item => {
    const newBreadcrumb = [...breadcrumb]
    if (item.name) {
      newBreadcrumb.push({
        title: item.name,
        path: item.redirect ? undefined : item.path,
      })
    }
    if (item.name && !item.redirect) {
      result.push([item.path, {
        title: item.name,
        breadcrumb: newBreadcrumb,
      }]);
    }

    if (item.routes) {
      result.push(...mapPathInfo(item.routes, [...newBreadcrumb]));
    }
  });
  return result;
};

/**
 * path对应name的Map
 */
export const pathInfoMap = new Map(mapPathInfo([layoutRoute]));


/**
 * 递归地将layoutRoute转换为[path, name][]
 */
const mapName = (routes: TLayoutRoute[]) => {
  const result: [string, string][] = [];
  routes.forEach(item => {
    if (item.name && !item.redirect) {
      result.push([item.path, item.name]);
    }

    if (item.routes) {
      result.push(...mapName(item.routes));
    }
  });
  return result;
};

/**
 * path对应name的Map
 */
export const pathNameMap = new Map(mapName([layoutRoute]));


export default router;