// import './StatisticCards.less'

import { apiStatisticInfo } from "@/apis/apis.api";
import { StatisticCard } from "@ant-design/pro-components";
import { useRequest } from "ahooks";

type StatisticCardsProps = {

};

export const StatisticCards = ({ }: StatisticCardsProps) => {
  const { data } = useRequest(async () => {
    return await apiStatisticInfo()
  }, {
    pollingInterval: 1000 * 60,
  })
  return (
    <div className="flex gap-4">
      <StatisticCard
        colSpan={3}
        title="今日累计停车"
        statistic={{
          value: data?.todayParkTotal,
          suffix: '辆',
          // description: <Statistic title="同比" value="6.47%" trend="up" />,
        }}
      />
      <StatisticCard
        colSpan={3}
        title="当前在库车辆"
        statistic={{
          value: data?.nowParkCount,
          suffix: '辆',
          // description: <Statistic title="同比" value="6.47%" trend="up" />,
        }}
      />

      <StatisticCard
        colSpan={3}
        title="用户日新增/总数"
        statistic={{
          value: data ? `${data.todayUserAdd}/${data.userTotal}` : '-/-',
          suffix: '人',
        }}
      />
      <StatisticCard
        colSpan={3}
        title="商家日新增/总数"
        statistic={{
          value: data ? `${data.todayMerchantUserAdd}/${data.merchantUserTotal}` : '-/-',
          suffix: '人',
        }}
      />

      <StatisticCard
        colSpan={3}
        title="登记三轮车数"
        statistic={{
          value: data?.tricycleTotal,
          suffix: '辆',
        }}
      />
      {/* <StatisticCard
        colSpan={3}
        title="累计三轮车保证金"
        statistic={{
          value: data?.tricycleDepositTotal,
          suffix: '元',
        }}
      />
      <StatisticCard
        colSpan={3}
        title="已收三轮车罚金"
        statistic={{
          value: data?.tricycleIllegalFineTotal,
          suffix: '元',
        }}
      /> */}
      <StatisticCard
        colSpan={3}
        title="上架商铺数"
        statistic={{
          value: data?.onShelfShopTotal,
          suffix: '个',
        }}
      />

    </div>
  );
}
