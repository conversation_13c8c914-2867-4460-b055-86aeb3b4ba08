import { FormLayout } from "@formily/antd-v5";
import { FormProvider } from "@formily/react";
import { SchemaField, useForm } from "@/components/Formily";
import { useModel } from 'foca';
import { settingModel } from '@/stores/settingModel';
import { useEffect } from "react";

const MerchantSetting = () => {
  const form = useForm({});
  const settingModelState = useModel(settingModel);

  useEffect(() => {
    form.setInitialValues(settingModelState.Merchant)
    form.setValues(settingModelState.Merchant)
  }, [settingModelState.Merchant])

  return (
    <FormProvider form={form}>
      <FormLayout layout="vertical" bordered>
        <SchemaField>
          <SchemaField.String
            name="serviceTel"
            title="服务电话"
            x-decorator="EditFormItem"
            x-component="Input"
            x-pattern="readPretty"
            x-decorator-props={{
              parentKey: 'Merchant',
            }}
            x-validator={[]}
          />
          <SchemaField.Number
            name="qrcodeFeeCent"
            title="三轮车二维码工本费"
            x-decorator="EditFormItem"
            x-component="MoneyCent"
            x-pattern="readPretty"
            x-decorator-props={{
              parentKey: 'Merchant',
            }}
            x-validator={[]}
          />
          <SchemaField.Number
            name="tricycleMakeFeePayExpireHours"
            title="三轮车工本费缴纳期限"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '超期自动回收号牌',
              parentKey: 'Merchant',
            }}
            x-component-props={{
              addonAfter: '小时',
            }}
            x-validator={[]}
          />
          <SchemaField.Number
            name="vehicleMonthlyPayExpireHours"
            title="机动车月卡费缴纳期限"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '超期状态转换为支付超时',
              parentKey: 'Merchant',
            }}
            x-component-props={{
              addonAfter: '小时',
            }}
            x-validator={[]}
          />
          <SchemaField.Number
            name="vehicleChangePlateFee"
            title="机动车换牌费"
            x-decorator="EditFormItem"
            x-component="MoneyCent"
            x-pattern="readPretty"
            x-decorator-props={{
              parentKey: 'Merchant',
            }}
            x-validator={[]}
          />
          <SchemaField.Array
            name="vehicleImagesNames"
            title="机动车审批图片名称"
            x-component="ArrayItems"
            x-decorator="EditFormItem"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '多个名称，每个名称对应一个图片',
              parentKey: 'Merchant',
            }}
          >
            <SchemaField.Void x-component="Space">
              <SchemaField.Void
                x-decorator="FormItem"
                x-component="ArrayItems.SortHandle"
              />
              <SchemaField.String
                x-decorator="FormItem"
                required
                name="input"
                x-component="Input"
                x-component-props={{
                  style: {
                    width: 600,
                  }
                }}
              />
              <SchemaField.Void
                x-decorator="FormItem"
                x-component="ArrayItems.Remove"
              />
              <SchemaField.Void
                x-decorator="FormItem"
                x-component="ArrayItems.Copy"
              />
            </SchemaField.Void>
            <SchemaField.Void
              x-component="ArrayItems.Addition"
              title="添加条目"
            />
          </SchemaField.Array>
          {/* <SchemaField.String
            name="tricycleDepositCent"
            title="三轮车保证金"
            x-decorator="EditFormItem"
            x-component="MoneyCent"
            x-pattern="readPretty"
            x-decorator-props={{
              parentKey: 'Merchant',
            }}
            x-validator={[]}
          />
          <SchemaField.String
            name="tricycleDepositWarnCent"
            title="三轮车保证金提醒补缴线"
            x-decorator="EditFormItem"
            x-component="MoneyCent"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '低于此金额，提醒补缴保证金',
              parentKey: 'Merchant',
            }}
            x-validator={[]}
          />
          <SchemaField.String
            name="tricycleDepositErrCent"
            title="三轮车保证金催缴线"
            x-decorator="EditFormItem"
            x-component="MoneyCent"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '低于此金额，拖走三轮车',
              parentKey: 'Merchant',
            }}
            x-validator={[]}
          />
          <SchemaField.Number
            name="tricycleFinePayExpireDays"
            title="三轮车罚款缴纳期限"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '超期自动从保证金中扣除',
              parentKey: 'Merchant',
            }}
            x-component-props={{
              addonAfter: '天',
            }}
            x-validator={[]}
          /> */}

          {/* <SchemaField.Array
            name="homeNavIcons"
            title="首页图标导航"
            x-decorator="EditFormItem"
            x-component="NavIcons"
            x-pattern="readPretty"
            x-decorator-props={{
              parentKey: 'Merchant',
            }}
            x-component-props={{
              cols: 5,
            }}
          /> */}
          <SchemaField.Number
            name="withdrawFeeRate"
            title="提现手续费率"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-component-props={{
              addonAfter: '‱',
            }}
            x-decorator-props={{
              tooltip: '提现手续费率，万分之几',
              parentKey: 'Merchant',
            }}
          />
        </SchemaField>
      </FormLayout>
    </FormProvider>
  );
}
export default MerchantSetting;