
import {
  connect,
  mapReadPretty,
  useField,
} from '@formily/react'
import { useEffect, useRef, useState } from 'react';



const LocationPicker = (props: any) => {
  const field = useField() as any;
  const mapDom = useRef<HTMLDivElement>(null);
  const mapRef = useRef<any>(null);
  const markerLayer = useRef<any>(null);
  const [inited, setInited] = useState(false)

  useEffect(() => {
    if (inited && field.value) {
      const [lat, lng] = field.value.split(',').map(Number)
      // mapRef.current!.panTo(new TMap.LatLng(lat, lng));
      markerLayer.current!.setGeometries([
        {//第二个点标记
          "id": "1",
          "styleId": 'marker',
          "position": new TMap.LatLng(lat, lng),
          "properties": {
            "title": "marker1"
          }
        }
      ])
      return;
    }
  }, [
    field.value, inited
  ])

  useEffect(() => {
    const center = new TMap.LatLng(24.902771, 102.788196);//设置中心点坐标
    //初始化地图
    mapRef.current = new TMap.Map(mapDom.current, {
      center: center,
      showControl: false,
      zoom: 18,
    });
    markerLayer.current = new TMap.MultiMarker({
      map: mapRef.current,  //指定地图容器
      geometries: []
    });
    setInited(true);
    mapRef.current.on('click', (e) => {
      field.setValue(e.latLng.lat + ',' + e.latLng.lng)
    });
  }, [])

  const canScrollBody = (can: boolean) => {
    document.getElementById('main-content')!.style.overflow = can ? 'auto' : 'hidden'
  }

  return (
    <div ref={mapDom} className='w-full h-[600px]' onMouseEnter={() => canScrollBody(false)} onMouseLeave={() => canScrollBody(true)}></div>
  );
};



export default connect(
  LocationPicker,
  mapReadPretty(({ value }) => value),
);
