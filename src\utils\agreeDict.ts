export type TAgreeOption = {
  value: string;
  label: string;
  data?: any; // 额外数据
  children?: TAgreeOption[];
};

export type TAgreeMapOption = TAgreeOption & {
  parentValues: string[];
  childrenValues: string[];
  upValues: string[];
  downValues: string[];
};

export type TAgreeDict = {
  name: string;
  options: TAgreeOption[];
  labels: string[];
  values: string[];
  valueLabelMap: Record<string, string>;
  labelValueMap: Record<string, string>;
  valueDataMap: {
    [key: string]: TAgreeMapOption;
  };
  labelDataMap: {
    [key: string]: TAgreeMapOption;
  };
  match(value: string | number, ...labels: string[]): boolean;
};

export type TBaseAgreeDicts = typeof BaseAgreeDicts;

export type TAgreeDicts = {
  [key in keyof TBaseAgreeDicts]: TAgreeDict;
};



export const BaseAgreeDicts = {
  Feedback_status: {
    name: '反馈状态',
    options: [
      { value: 'PENDING', label: '待处理' },
      { value: 'PROCESSED', label: '已处理' },
    ],
  },
  VipFeeOrder_status: {
    name: '会员费订单状态',
    options: [
      { value: 'WAIT_PAY', label: '待支付' },
      { value: 'PAID', label: '已支付' },
      { value: 'CLOSED', label: '已关闭' },
    ],
  },

  Bill_payStatus: {
    name: '订单支付状态',
    options: [
      { value: 'UNPAID', label: '未支付' },
      { value: 'PAID', label: '已支付' },
      { value: 'REFUNDING', label: '退款中' },
      { value: 'REFUNDED', label: '已退款' },
      { value: 'PART_REFUNDED', label: '部分退款' },
      { value: 'CLOSED', label: '订单关闭' },
      { value: 'MANUAL', label: '人工处理' },
    ],
  },

  Bill_orderType: {
    name: '订单类型',
    options: [
      { value: 'VIP_FEE', label: '会员费' },
      { value: 'WITHDRAW', label: '提现' },
    ],
  },

  
  ClientUserPointsRecord_recordType: {
    name: '积分记录类型',
    options: [
      { value: 'REFERRAL_COMMISSION', label: '推荐返佣', data: { color: 'orange' } },
      { value: 'MANUAL_ADJUSTMENT', label: '手动调整', data: { color: 'blue' } },
      { value: 'CONSUMPTION', label: '消费扣减', data: { color: 'red' } },
      { value: 'REFUND', label: '退款返还', data: { color: 'green' } },
    ],
  },

  ClientMessage_messageType: {
    name: '消息类型',
    options: [
      { value: 'SYSTEM', label: '系统消息' },
      { value: 'PAYMENT', label: '支付相关' },
      { value: 'VIP', label: 'VIP相关' },
      { value: 'PROMOTION', label: '推广活动' },
      { value: 'NOTIFICATION', label: '通知消息' },
      { value: 'FEEDBACK', label: '反馈回复' },
    ]
  }
  
};

function handleBaseAgreeDicts() {
  const result = {} as Record<string, TAgreeMapOption>;
  function tranToValueDataMap(treeOptions: TAgreeOption[], parentValues: string[] = [], parentChildrenValues: string[] = []) {
    let result: Record<string, TAgreeMapOption> = {};
    for (const item of treeOptions) {
      parentChildrenValues.push(item.value);
      const childrenValues: string[] = [];
      if (item.children) {
        const childrenMap = tranToValueDataMap(item.children, [...parentValues, item.value], childrenValues);
        result = { ...result, ...childrenMap };
        parentChildrenValues.push(...childrenValues);
      }
      result[item.value] = {
        ...item,
        parentValues: [...parentValues],
        childrenValues,
        upValues: [...parentValues, item.value],
        downValues: [item.value, ...childrenValues],
      };
    }
    return result as Record<string, TAgreeMapOption>;
  }
  for (const key in BaseAgreeDicts) {
    const valueDataMap = tranToValueDataMap((BaseAgreeDicts as any)[key].options);
    (result as any)[key] = {
      name: (BaseAgreeDicts as any)[key].name,
      options: (BaseAgreeDicts as any)[key].options,
      values: Object.keys(valueDataMap),
      labels: Object.values(valueDataMap).map((item) => item.label),
      valueLabelMap: Object.values(valueDataMap).reduce((prev: any, item) => {
        return { ...prev, [item.value]: item.label };
      }, {}),
      labelValueMap: Object.values(valueDataMap).reduce((prev, item) => {
        return { ...prev, [item.label]: item.value };
      }, {}),
      valueDataMap,
      labelDataMap: Object.values(valueDataMap).reduce((prev, item) => {
        (prev as any)[item.label] = item;
        return prev;
      }, {}),
      match(value: string | number, ...labels: string[]) {
        return labels.some((label) => this.labelValueMap[label].toString() === value.toString());
      }
    };
  }
  return result as unknown as TAgreeDicts;
}

export const AgreeDicts = handleBaseAgreeDicts();
