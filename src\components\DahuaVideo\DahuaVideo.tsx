import { useEffect, useRef, useState } from "react"

export interface TDahuaVideoProps {

}

function DahuaVideo(props: TDahuaVideoProps) {

  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const playerRef = useRef<any>(null);

  const [inited, setInited] = useState(false)

  useEffect(() => {
    const options = {
      wsURL: `ws://10.8.0.1:7045/rtspoverwebsocket`,
      rtspURL: `rtsp://10.8.0.1:7045/cam/realmonitor?channel=1&subtype=1&proto=Private3`,
      username: 'admin',
      password:  'admin@123',
    }
    const player = new PlayerControl(options)
    player.on('WorkerReady', function () {
      setInited(true)
    })
    player.init(canvasRef.current, videoRef.current) //初始化播放器
    playerRef.current = player
  }, [])

  useEffect(() => {
    if (inited) {
      
      // playerRef.current.play()
    }
  }, [inited])

  return (
    <div className="DahuaVideo">
      <video ref={videoRef}></video>
      <canvas ref={canvasRef}></canvas>
    </div>
  )
}

export default DahuaVideo
