import { FormLayout } from "@formily/antd-v5";
import { FormProvider } from "@formily/react";
import { SchemaField, useForm } from "@/components/Formily";
import { useModel } from 'foca';
import { settingModel } from '@/stores/settingModel';
import { useEffect } from "react";

const JobSetting = () => {
  const form = useForm({});
  const settingModelState = useModel(settingModel);

  useEffect(() => {
    form.setInitialValues(settingModelState.Job)
    form.setValues(settingModelState.Job)
  }, [settingModelState.Job])

  return (
    <FormProvider form={form}>
      <FormLayout layout="vertical" bordered>
        <SchemaField>
          <SchemaField.String
            name="jobServiceAppointmentSuccessNotice"
            title="园区服务预约接受通知"
            x-decorator="EditFormItem"
            x-component="Input"
            x-pattern="readPretty"
            x-component-props={{
              maxLength: 20,
            }}
            x-decorator-props={{
              tooltip: '园区服务预约接受后给商家的通知内容',
              parentKey: 'Job',
            }}
          />

        </SchemaField>


      </FormLayout>
    </FormProvider>
  );
}
export default JobSetting;