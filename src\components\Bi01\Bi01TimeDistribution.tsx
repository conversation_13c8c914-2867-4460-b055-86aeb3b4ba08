import { apiBiPeopleCountInAndOutReport } from "@/apis/apis.api";
import { useEcharts } from "@/hooks/useEcharts";
import { useStepedRequest } from "@/hooks/useStepedRequest";
import { settingModel } from "@/stores/settingModel";
import { useCreation, useRequest } from "ahooks"
import dayjs from "dayjs";
import { useModel } from "foca";
import { useEffect } from "react";

const Bi01TimeDistribution = () => {

    const settingModelState = useModel(settingModel);
    const peopleCountRate = useCreation(() => settingModelState?.Admin?.peopleCountRate || 1, [settingModelState?.Admin?.peopleCountRate]);
    // refreshDeps: [peopleCountRate]

    const apiBiPeopleCountInAndOutReportSteped = useStepedRequest(apiBiPeopleCountInAndOutReport);


    const { data } = useRequest(async () => {
        const start = dayjs().subtract(24, 'hour').format('YYYY-MM-DD HH');
        const end = dayjs().subtract(1, 'hour').format('YYYY-MM-DD HH');
        const res = await apiBiPeopleCountInAndOutReportSteped({
            timeUnit: 'hour',
            start: start,
            end: end
        })
        const timeRanges = [
            { name: '凌晨', time: '00:00-06:00', value: 0 },
            { name: '上午', time: '06:00-12:00', value: 0 },
            { name: '下午', time: '12:00-18:00', value: 0 },
            { name: '夜晚', time: '18:00-24:00', value: 0 },
        ];
        for (let i = 0; i < res.in.length; i++) {
            for (let j = 0; j < res.in[i].data.length; j ++) {
                const hour = dayjs('2020-01-01 ' + res.in[i].data[j].showTime).hour()
                const timeRangeIndex = Math.floor(hour / 6) 
                const count = Math.round((res.in[i].data[j].remainder || 0) * peopleCountRate)
                timeRanges[timeRangeIndex].value += count
            }
        }
        return timeRanges
    }, {
        pollingInterval: 60 * 60 * 1000,
        refreshDeps: [peopleCountRate]
    })

    const { chart, setContainerRef } = useEcharts({
        theme: 'walden',
    });

    useEffect(() => {
        if (chart && data) {

            chart.setOption({
                grid: {
                    left: 0,
                    right: '20%',
                    bottom: 0,
                    top: 0,
                    containLabel: true
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}<br/>{c} 人 ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    right: '5%',
                    top: 'center',
                    itemGap: 20,
                    textStyle: {
                        color: '#C9E8F2',
                        rich: {
                            name: {
                                width: 100,
                                fontSize: 14,
                                color: '#C9E8F2'
                            },
                            time: {
                                width: 100,
                                fontSize: 14,
                                color: '#91e4ff'
                            },
                            value: {
                                width: 100,
                                fontSize: 14,
                                color: '#91e4ff'
                            }
                        }
                    },
                    formatter: function (name) {
                        const item = data.find(d => d.name === name);
                        const percent = (item.value / data.reduce((sum, d) => sum + d.value, 0) * 100).toFixed(1);
                        return [
                            '{name|' + name + '  ' + percent + '%}',
                            '{time|' + item.time + '}',
                            '{value|' + item.value.toLocaleString() + ' 人}'
                        ].join(' ');
                    }
                },
                series: [
                    {
                        name: '时间分布',
                        type: 'pie',
                        radius: ['20%', '70%'],
                        center: ['20%', '50%'],
                        roseType: 'area',
                        itemStyle: {
                            borderRadius: 5
                        },
                        label: {
                            show: false
                        },
                        data: data.map(item => ({
                            name: item.name,
                            value: item.value
                        }))
                    }
                ]
            });
        }
    }, [chart, data]);

    return <div className="w-full h-full relative">
        <img className="absolute left-[25px] top-[10px]" src="/images/bi/pie_bg.webp" alt="" />
        <div ref={setContainerRef} className="w-full h-full"></div>
    </div>;
}

export default Bi01TimeDistribution;
