import { ToolsUtil } from "./tools";

export enum NVR_ERROR_CODE {
    UNKNOWN = 1000, //未知错误
    NETWORKERROR = 1001, //网络错误
    PARAMERROR = 1002, //缺少插件元素
    LOGIN_NOLOGIN = 2000, // 未登录
    LOGIN_REPEATLOGIN = 2001, //设备已登录，重复登录
    LOGIN_NOSUPPORT = 2002, //当前设备不支持Digest登录
    PLAY_PLUGININITFAIL = 3000, //插件初始化失败
    PLAY_NOREPEATPLAY = 3001, //当前窗口已经在预览
    PLAY_PLAYBACKABNORMAL = 3002, //回放异常
    PLAY_PLAYBACKSTOP = 3003, //回放停止
    PLAY_NOFREESPACE = 3004, //录像过程中，硬盘容量不足
    TALK_FAIL = 5000, //语音对讲失败
}

export class NvrUtils {
    static szDeviceIdentify = "";
    static initPlugin(options?: {
        cbEvent?: (iEventType: number, iParam1: number, iParam2: number) => void,
        cbInitPluginComplete?: () => void
        bWndFull?: boolean, // 单窗口双击全屏，默认支持，true(支持)，false(不支持)。
        iWndowType?: number, // 分屏类型：1- 1*1，2- 2*2，3- 3*3，4- 4*4 默认值为1，单画面
        bDebugMode?: boolean
    }) {
        const {
            cbEvent = () => { },
            cbInitPluginComplete = () => { },
            bWndFull = false,
            iWndowType = 1,
            bDebugMode = false
        } = options || {};
        return new Promise((resolve, reject) => {
            try {
                WebVideoCtrl.I_InitPlugin({
                    bWndFull,     //是否支持单窗口双击全屏,true:支持 false:不支持
                    iWndowType,
                    bDebugMode,
                    cbEvent,
                    cbInitPluginComplete: () => {
                        cbInitPluginComplete();
                        resolve(true);
                    }
                });
            } catch (e) {
                reject(e);
            }
        });
    }

    static insertOBJECTPlugin(domId: string) {
        return new Promise((resolve, reject) => {
            WebVideoCtrl.I_InsertOBJECTPlugin(domId).then((res) => {
                resolve(res);
            }, (e) => {
                reject(e);
            });
        });
    }

    static login(options: {
        szIP: string,
        szPort: number,
        szUsername: string,
        szPassword: string
    }) {
        const { szIP, szPort, szUsername, szPassword } = options;
        return new Promise((resolve, reject) => {
            WebVideoCtrl.I_Login(szIP, 1, szPort, szUsername, szPassword, {
                timeout: 3000,
                success: () => {
                    this.szDeviceIdentify = szIP + "_" + szPort;
                    resolve(true);
                },
                error: (error) => {
                    if (NVR_ERROR_CODE.LOGIN_REPEATLOGIN === error.errorCode) {
                        console.log("NVR 已登录过！");
                        resolve(true);
                    } else {
                        console.log("NVR 登录失败,", error.errorCode, error.errorMsg);
                        reject(error);
                    }
                }
            });
        });
    }

    static async logout() {
        return WebVideoCtrl.I_Logout(this.szDeviceIdentify);
    }

    static async getDevicePort() {
        return WebVideoCtrl.I_GetDevicePort(this.szDeviceIdentify);
    }

    static async getChannelInfoFull() {
        const channels = await new Promise((resolve, reject) => {
            const channels = [];
            let completedCount = 0;
            let failedCount = 0;
            const functionsCount = 3;
            const complete = (success: boolean) => {
                completedCount++;
                if (!success) {
                    failedCount++;
                }
                if (failedCount == functionsCount) {
                    reject("获取通道失败");
                } else if (completedCount == functionsCount) {
                    resolve(channels);
                }
            }
            // 模拟通道
            WebVideoCtrl.I_GetAnalogChannelInfo(this.szDeviceIdentify, {
                success: function (xmlDoc) {
                    var oChannels = $(xmlDoc).find("VideoInputChannel");

                    $.each(oChannels, function (i) {
                        var id = $(this).find("id").eq(0).text(),
                            name = $(this).find("name").eq(0).text();
                        if ("" == name) {
                            name = "Camera " + (i < 9 ? "0" + (i + 1) : (i + 1));
                        }
                        channels.push({
                            id,
                            name,
                            type: "Analog"
                        });
                        // oSel.append("<option value='" + id + "' bZero='false'>" + name + "</option>");
                    });
                    console.log(" 获取模拟通道成功！");
                    complete(true);
                },
                error: function (oError) {
                    console.log(" 获取模拟通道失败！", oError.errorCode, oError.errorMsg);
                    complete(false);
                }
            });
            // 数字通道
            WebVideoCtrl.I_GetDigitalChannelInfo(this.szDeviceIdentify, {
                success: function (xmlDoc) {
                    var oChannels = $(xmlDoc).find("InputProxyChannelStatus");

                    $.each(oChannels, function (i) {
                        var id = $(this).find("id").eq(0).text(),
                            name = $(this).find("name").eq(0).text(),
                            online = $(this).find("online").eq(0).text();
                        if ("false" == online) {// 过滤禁用的数字通道
                            return true;
                        }
                        if ("" == name) {
                            name = "IPCamera " + (i < 9 ? "0" + (i + 1) : (i + 1));
                        }
                        channels.push({
                            id,
                            name,
                            type: "Digital"
                        });
                    });

                    console.log(" 获取数字通道成功！");
                    complete(true);
                },
                error: function (oError) {
                    console.log(" 获取数字通道失败！", oError.errorCode, oError.errorMsg);
                    complete(false);
                }
            });
            // 零通道
            WebVideoCtrl.I_GetZeroChannelInfo(this.szDeviceIdentify, {
                success: function (xmlDoc) {
                    var oChannels = $(xmlDoc).find("ZeroVideoChannel");

                    $.each(oChannels, function (i) {
                        var id = $(this).find("id").eq(0).text(),
                            name = $(this).find("name").eq(0).text();
                        if ("" == name) {
                            name = "Zero Channel " + (i < 9 ? "0" + (i + 1) : (i + 1));
                        }
                        if ("true" == $(this).find("enabled").eq(0).text()) {// 过滤禁用的零通道
                            channels.push({
                                id,
                                name,
                                type: "Zero"
                            });
                        }
                    });
                    console.log(" 获取零通道成功！");
                    complete(true);
                },
                error: function (oError) {
                    console.log(" 获取零通道失败！", oError.errorCode, oError.errorMsg);
                    complete(false);
                }
            });
        })

        return channels

    }
    static async getChannelInfo() {
        return new Promise((resolve, reject) => {
            WebVideoCtrl.I_GetDigitalChannelInfo(this.szDeviceIdentify, {
                success: (xmlDoc) => {
                    var oChannels = $(xmlDoc).find("InputProxyChannelStatus");
                    const channels = [];
                    $.each(oChannels, function (i) {
                        let id = $(this).find("id").eq(0).text(),
                            name = $(this).find("name").eq(0).text(),
                            online = $(this).find("online").eq(0).text();
                        if ("false" == online) {// 过滤禁用的数字通道
                            return true;
                        }
                        if ("" == name) {
                            name = "IPCamera_" + (i < 9 ? "0" + (i + 1) : (i + 1));
                        }
                        channels.push({
                            id,
                            name,
                            type: "Digital"
                        });
                    });
                    resolve(channels);
                },
                error: (error) => {
                    console.log(" 获取数字通道失败！", error.errorCode, error.errorMsg);
                    reject(error);

                }
            });

        })

    }

    static startRealPlay(options: {
        iWndIndex?: number,
        iStreamType?: number, // 码流类型1-主码流，2-子码流
        iChannelID?: number, // 通道号
        bZeroChannel?: boolean, // 是否播放零通道，默认为false
        iPort?: number
    }) {
        const {
            iWndIndex = 0,
            iStreamType = 1,
            iChannelID = 0,
            bZeroChannel = false,
            iPort = 554,
        } = options || {};
        return new Promise((resolve, reject) => {
            WebVideoCtrl.I_StartRealPlay(this.szDeviceIdentify, {
                iWndIndex,
                iStreamType: iStreamType, // 码流类型1-主码流，2-子码流，默认使用主码流预览
                iChannelID: iChannelID,
                bZeroChannel: bZeroChannel,
                iPort,
                success: function () {
                    console.log("开始预览成功");
                    resolve(true);
                },
                error: function (error) {
                    console.log("开始预览失败", error.errorCode, error.errorMsg);
                    reject(error);
                }
            });
        });
    }

    static stopRealPlay() {
        return new Promise((resolve, reject) => {
            WebVideoCtrl.I_Stop({
                iWndIndex: 0,
                success: function () {
                    console.log("停止预览成功");
                    resolve(true);
                },
                error: function (error) {
                    console.log("停止预览失败", error.errorCode, error.errorMsg);
                    reject(error);
                }
            });
        });
    }

    static startPlayback(options: {
        szStartTime: string,
        szEndTime: string,
        iWndIndex?: number,
        iStreamType?: number, // 码流类型1-主码流，2-子码流
        iChannelID?: number, // 通道号
        iPort?: number,
        oTransCodeParam?: {
            TransFrameRate: string,  // 取值范围：0-全部， 5-1，6-2，7-4，8-6，9-8，10-10，11-12，12-16，13-20，14-15，15-18，16－22， 255-自动（和源一致）
            TransResolution: string, // 分辨率 1-CIF(352*288/352*240) ，2-QCIF(176*144/176*120) ，3-4CIF(704*576/704*480) 或D1(720*576/720*486)，255-Auto(使用当前码流分辨率) 
            TransBitrate: string,  // 表示码率 2-32K，3-48k，4-64K，5-80K，6-96K，7-128K，8-160k，9-192K，10-224K，11-256K，12-320K，13-384K，14-448K，15-512K，16-640K，17-768K，18-896K，19-1024K，20-1280K，21-1536K，22-1792K，23-2048K，24-3072K，25-4096K，26-8192K，255-  自动（和源一致）
        }
    }) {
       
        const {
            szStartTime,
            szEndTime,
            iWndIndex = 0,
            iStreamType = 1,
            iChannelID = 0,
            iPort = 554,
            oTransCodeParam = {

                TransFrameRate: "14",// 0：全帧率，5：1，6：2，7：4，8：6，9：8，10：10，11：12，12：16，14：15，15：18，13：20，16：22
                TransResolution: "1",// 255：Auto，3：4CIF，2：QCIF，1：CIF
                TransBitrate: "19"// 2：32K，3：48K，4：64K，5：80K，6：96K，7：128K，8：160K，9：192K，10：224K，11：256K，12：320K，13：384K，14：448K，15：512K，16：640K，17：768K，18：896K，19：1024K，20：1280K，21：1536K，22：1792K，23：2048K，24：3072K，25：4096K，26：8192K

            },
        } = options || {};
        console.log("开始回放", {
            szStartTime,
            szEndTime,
            iWndIndex,
            iStreamType,
            iChannelID,
            iPort,
            oTransCodeParam,
        });
        return new Promise((resolve, reject) => {
            WebVideoCtrl.I_StartPlayback(this.szDeviceIdentify, {
                szStartTime,
                szEndTime,
                iWndIndex,
                iStreamType,
                iChannelID,
                iPort,
                // oTransCodeParam,
                success: function () {
                    console.log("开始回放成功");
                    resolve(true);
                },
                error: function (error) {
                    console.log("开始回放失败", error.errorCode, error.errorMsg);
                    reject(error);
                }
            });
        });
    }
}