import ReactDOM from 'react-dom/client'
import { ConfigProvider as AntdConfigProvider, App } from "antd";
import zhCN from "antd/locale/zh_CN";
import 'virtual:uno.css'
import { FocaProvider, store } from 'foca';
import { RouterProvider } from 'react-router-dom';
import { ToolsUtil } from './utils/tools';
import router from './router';
import 'default-passive-events'; // 解决 passive event listener 问题
import './index.less'

import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import duration from 'dayjs/plugin/duration';
import relativeTime  from 'dayjs/plugin/relativeTime';
dayjs.locale('zh-cn') 
dayjs.extend(duration)
dayjs.extend(relativeTime)

store.init(ToolsUtil.isDebug ? {
  compose: 'redux-devtools',
} : {});

console.log('0.1.5')

ReactDOM.createRoot(document.getElementById('root')!).render(
  <FocaProvider>
    <AntdConfigProvider locale={zhCN}>
      <App>
        <RouterProvider router={router} />
      </App>
    </AntdConfigProvider>
  </FocaProvider>,
)
