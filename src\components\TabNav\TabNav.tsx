


import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useGetState, useMemoizedFn } from 'ahooks';
import { keepAlivePathSet, pathNameMap } from '@/router';
import { useAliveController } from 'react-activation';
import { Dropdown } from 'antd';
import { StopOutlined, ReloadOutlined, CloseOutlined, VerticalLeftOutlined, VerticalAlignMiddleOutlined, VerticalRightOutlined } from '@ant-design/icons';
import clsx from 'clsx';
import './TabNav.less';

interface TTabItem {
  title: string;
  pathname: string;
  state: unknown;
}


function TabNav() {
  const location = useLocation();
  const navigate = useNavigate();
  const [tabs, setTabs, getTabs] = useGetState<TTabItem[]>([]);
  const aliveController = useAliveController();

  useEffect(() => {
    const currentTabs = getTabs();
    const hasTab = currentTabs.some((item) => item.pathname === location.pathname);
    if (!hasTab && pathNameMap.has(location.pathname)) {
      const startIndex = Math.max(0, currentTabs.length - 14);
      setTabs([...currentTabs, {
        title: pathNameMap.get(location.pathname) || '',
        pathname: location.pathname,
        state: location.state
      }].slice(startIndex));
    }
  }, [getTabs, location, setTabs]);

  const onTabRemove = useMemoizedFn((index: number) => {
    const currentTabs = getTabs();
    if (currentTabs.length === 1) {
      return;
    }
    const nextTab = index === 0 ? currentTabs[1] : currentTabs[index - 1];
    const dropPathname = currentTabs[index].pathname;
    navigate(nextTab.pathname, { state: nextTab.state });
    setTabs(currentTabs.filter((_item, i) => i !== index));
    if (keepAlivePathSet.has(dropPathname)) {
      aliveController?.drop && aliveController.drop(dropPathname);
    }

  });

  const onTabsChange = useMemoizedFn((nextTab: TTabItem) => {
    navigate(nextTab.pathname, { state: nextTab.state });
  });

  const onContextMenuClick = (key: string, index: number) => {
    switch (key) {
      case "reload":
        handleReloadTab(index);
        break;
      case "close":
        onTabRemove(index);
        break;
      case "close-left":
        handleRemoveTabs(index, "left");
        break;
      case "close-right":
        handleRemoveTabs(index, "right");
        break;
      case "close-other":
        handleRemoveTabs(index, "other");
        break;
      default:
        break;
    }
  };

  const handleReloadTab = useMemoizedFn((index: number) => {
    const pathname = tabs[index].pathname;
    if (keepAlivePathSet.has(pathname)) {
      aliveController?.refresh && aliveController?.refresh(pathname);
    }
  });

  const handleRemoveTabs = useMemoizedFn((index: number, type: "left" | "right" | "other") => {
    const removeTabs = tabs.filter((_item, i) => {
      if (type === "left") {
        return i < index;
      } else if (type === "right") {
        return i > index;
      } else {
        return i !== index;
      }
    });
    removeTabs.forEach((item) => {
      aliveController?.drop && aliveController?.drop(item.pathname);
    });
    const newTabs = tabs.filter((_item, i) => {
      if (type === "left") {
        return i >= index;
      } else if (type === "right") {
        return i <= index;
      } else {
        return i === index;
      }
    });
    setTabs(newTabs);

  });

  return (
    <div className="TabNav">
      <div className='TabNav__tabs'>
        {tabs.map((item, index) => (
          <div className={clsx('TabNav__tabs__item', {
            'TabNav__tabs__item--active': item.pathname === location.pathname,
          })} key={item.pathname}
            onClick={() => onTabsChange(item)}
          >
            <Dropdown menu={{
              onClick: ({ key }) => onContextMenuClick(key, index),
              items: [
                {
                  label: '刷新',
                  key: 'reload',
                  icon: <ReloadOutlined />,
                  __show: item.pathname === location.pathname && keepAlivePathSet.has(item.pathname),
                },
                {
                  label: '关闭',
                  key: 'close',
                  icon: <CloseOutlined />,
                  __show: tabs.length > 1,
                },
                {
                  label: '关闭左侧',
                  key: 'close-left',
                  icon: <VerticalRightOutlined />,
                  __show: item.pathname === location.pathname && index !== 0,
                },
                {
                  label: '关闭右侧',
                  key: 'close-right',
                  icon: <VerticalLeftOutlined />,
                  __show: item.pathname === location.pathname && index !== tabs.length - 1,
                },
                {
                  label: '关闭其他',
                  key: 'close-other',
                  icon: <VerticalAlignMiddleOutlined />,
                  __show: item.pathname === location.pathname && tabs.length > 1,
                },
              ].filter(item => item.__show).map(item => {
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                const { __show, ...data } = item;
                return data;
              })
            }} trigger={['contextMenu']}>
              <div className='TabNav__tabs__item__title'>{item.title}</div>
            </Dropdown>
            {item.pathname === location.pathname ? (
              <div
                className="TabNav__tabs__item__btn"
                onClick={(e) => {
                  e.stopPropagation();
                  onTabRemove(index);
                }}
              >
                {tabs.length > 1 ? <CloseOutlined /> : <StopOutlined />}
              </div>
            ) : null}
          </div>
        ))}
      </div>
    </div>
  );
}

export default TabNav;
