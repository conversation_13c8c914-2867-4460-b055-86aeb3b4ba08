import {
  Input,
  Password,
  Select,
  TreeSelect,
  DatePicker,
  TimePicker,
  NumberPicker,
  Transfer,
  Cascader,
  Radio,
  Checkbox,
  Upload,
  Switch,
  FormLayout,
  FormItem,
  FormGrid,
  FormButtonGroup,
  Space,
  Submit,
  Reset,
  ArrayCards,
  ArrayItems,
  ArrayTable,
  ArrayTabs,
  FormCollapse,
  FormStep,
  FormTab,
  Editable,
  PreviewText,
  SelectTable,

  
 
} from "@formily/antd-v5";
import { createSchemaField } from "@formily/react";
import { createForm, Field, FormPathPattern, IFormProps, onFieldReact } from "@formily/core";
import { useMemo } from "react";
import { action } from '@formily/reactive'
import { Card } from "antd";
import FileUpload from "./FileUpload";
import RichText from "./RichText";
import DictOptions from "./DictOptions";
import SizedBox from "./SizedBox";
import MediaSize from "./MediaSize";
import MoneyCent from "./MoneyCent";
import NavIcons from "./NavIcons";
import StoreNavIcons from "./StoreNavIcons";
import LocationPicker from "./LocationPicker";

export const SchemaField = createSchemaField({
  components: {
    Card,
    Input,
    Password,
    Select,
    TreeSelect,
    DatePicker,
    TimePicker,
    NumberPicker,
    Transfer,
    Cascader,
    Radio,
    Checkbox,
    Upload,
    Switch,
    FormLayout,
    FormItem,
    FormGrid,
    FormButtonGroup,
    Space,
    Submit,
    Reset,
    ArrayCards,
    ArrayItems,
    ArrayTable,
    ArrayTabs,
    FormCollapse,
    FormStep,
    FormTab,
    Editable,
    PreviewText,
    FileUpload,
    RichText,
    DictOptions,
    SizedBox,
    MediaSize,
    SelectTable,
    MoneyCent,
    NavIcons,
    StoreNavIcons,
    LocationPicker,
  },
  scope: {},
});


type GetPropsType<T> = T extends (props: infer P) => void ? P : unknown;
export type TSchemaFieldProps = GetPropsType<typeof SchemaField>;

export function useForm<TFormData extends object>(options?: IFormProps<TFormData>) {
  return useMemo(() => createForm<TFormData>(options), []);
}

export const useAsyncDataSource = (
  pattern: FormPathPattern,
  service: (field: Field) => Promise<{ label: string; value: any }[]>
) => {
  onFieldReact(pattern, (field: any) => {
    field.loading = true
    service(field).then(
      action.bound((data) => {
        field.dataSource = data
        field.loading = false
      })
    )
  })
}