import { apiVipFeeTypePageList, apiVipFeeTypeUpdate } from "@/apis/apis";
import { useVipFeeTypeFormModal } from "@/hooks/useVipFeeTypeFormModal";
import { ToolsUtil } from "@/utils/tools";
import { PageContainer, ProDescriptions } from "@ant-design/pro-components";
import { Button, Card, Spin, Switch } from "antd";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

function VipFeeTypeDetail() {
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<TVipFeeType | null>(null);
  const params = new URLSearchParams(location.search);
  const id = params.get('id');

  const { showEditModal, formModalHolder } = useVipFeeTypeFormModal(() => {
    fetchData();
  });

  useEffect(() => {
    if (id) {
      fetchData();
    }
  }, [id]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const res = await apiVipFeeTypePageList({
        id: id as string,
        pageIndex: 0,
        pageSize: 1
      });
      if (res.rows && res.rows.length > 0) {
        setData(res.rows[0]);
      } else {
        setData(null);
      }
    } catch (error) {
      console.error('获取会员费类型详情失败', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageContainer
      title="会员费类型详情"
      onBack={() => navigate(-1)}
      extra={data ? [
        <Button key="edit" type="primary" onClick={() => showEditModal(data)}>
          编辑
        </Button>,
      ] : []}
    >
      <Card>
        {loading ? (
          <div style={{ textAlign: 'center', padding: 20 }}>
            <Spin />
          </div>
        ) : data ? (
          <ProDescriptions
            column={2}
            title={false}
            dataSource={data}
            columns={[
              { title: 'ID', dataIndex: 'id' },
              { title: '名称', dataIndex: 'name' },
              { title: '金额', dataIndex: 'amountCent', render: (text: number) => ToolsUtil.moneyCentFormat(text) },
              { title: '有效天数', dataIndex: 'days' },
              {
                title: '是否启用',
                dataIndex: 'isEnabled',
                render: (_, record) => (
                  <Switch checked={record.isEnabled} onChange={async (checked) => {
                    await apiVipFeeTypeUpdate({ id: record.id, isEnabled: checked });
                    setData({ ...record, isEnabled: checked });
                  }} />
                )
              },
              {
                title: '是否公开',
                dataIndex: 'isPublic',
                render: (_, record) => (
                  <Switch checked={record.isPublic} onChange={async (checked) => {
                    await apiVipFeeTypeUpdate({ id: record.id, isPublic: checked });
                    setData({ ...record, isPublic: checked });
                  }} />
                )
              },
              { title: '创建时间', dataIndex: 'createdAt', valueType: 'dateTime' },
            ]}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: 20 }}>未找到会员费类型信息</div>
        )}
      </Card>
      {formModalHolder}
    </PageContainer>
  );
}

export default VipFeeTypeDetail;
