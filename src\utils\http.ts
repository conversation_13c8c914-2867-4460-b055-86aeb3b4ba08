import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import CacheUtils from './cache';
import router from '@/router';
import { message } from 'antd';
import waitUntil from 'async-wait-until';

const axiosInstance = axios.create();

export interface HttpOptions extends AxiosRequestConfig {
  url: string;
  method?: string;
  baseURL?: string;
  /**放在url上的参数 */
  query?: object;
  /**放在body上的参数 */
  data?: object;
  headers?: Record<string, any>;
  /** 是否自动处理响应 */
  autoHandleResponse?: boolean;
  /**是否自动处理错误 */
  autoHandleError?: boolean;
}

export type THttpRes<T = unknown> = {
  code: number;
  msg: string;
  data: T;
};

export class HttpUtils {
  static logined = false;

  static TOKEN = CacheUtils.getItem<string>('TOKEN') || '';

  static API_SITE = import.meta.env.VITE_API_SITE + '/api/admin';

  static async request<T = unknown>(options: HttpOptions): Promise<T> {
   
   
    const {
      url,
      baseURL = this.API_SITE,
      method = 'GET',
      data = {},
      headers = {},
      query = {},
      autoHandleError = true,
      autoHandleResponse = true,
      ...otherOptions
    } = options || {};

    headers['Authorization'] = this.TOKEN ? `Bearer ${this.TOKEN}` : '';

    const config: AxiosRequestConfig = {
      url,
      baseURL,
      method,
      params: query,
      data,
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        ...headers,
      },
      validateStatus: (status: number) => status >= 200 && status < 400,
      ...(otherOptions || {}),
    };
    try {
      console.log(config)
      const res = await axiosInstance(config);
      console.log(res)
      if (autoHandleResponse) {
        const resData = res.data as THttpRes<T>;
        if (resData.code === 401) {
          router.navigate('/login', {
            replace: true,
          });
          return;
        }
        if (resData.code !== 0) {
          throw resData;
        } else {
          return resData.data as T;
        }
      } else {
        return res as T;
      }
    } catch (err) {
      const error = err as {
        code?: number;
        msg?: string;
        response?: AxiosResponse;
      };

      let messageContent = '';
      if (error && error.msg) {
        messageContent = error.msg;
      }

      messageContent =
        messageContent ||
        error.response?.data?.message ||
        {
          401: '登录状态过期，请重新进入',
          403: '权限不足',
          404: '内容不存在',
          422: '参数错误',
        }[error.response?.status || 400] ||
        '未知请求错误';

      if (autoHandleError) {
        message.error(messageContent);
      }
      return Promise.reject({
        code: error.code || error.response?.data?.code || error.response?.status,
        message: messageContent,
      });
    }
  }

  static get<T = unknown>(url: string, query?: object, otherOptions?: Omit<HttpOptions, 'url' | 'method' | 'query'>): Promise<T> {
    return this.request<T>({ url, method: 'GET', query, ...otherOptions });
  }

  static post<T = unknown>(url: string, data?: object, otherOptions?: Omit<HttpOptions, 'url' | 'method' | 'data'>): Promise<T> {
    return this.request<T>({ url, method: 'POST', data, ...otherOptions });
  }

  static setToken(token?: string, expiredAt?: number) {
    if(!token) {
      this.TOKEN = '';
      CacheUtils.removeItem('TOKEN');
    } else {
      this.TOKEN = token;
      CacheUtils.setItem('TOKEN', token, expiredAt);
    }
   
  }

  /**
   * 上传文件
   * @returns 上传后的文件url
   */
  static async uploadFile(file?: File) {
    const upload = async (f: File) => {
      const formData = new FormData();
      formData.append('file', f);
      const response = await this.request<{
        url: string
      }>({
        url: '/upload',
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.url
    };
    if (file) {
      return upload(file);
    }

    return new Promise<string>((resolve, reject) => {
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.accept = 'image/*';
      fileInput.multiple = false;
      fileInput.onchange = async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (!file) return;
        try {
          const url = await upload(file);
          resolve(url);
        } catch (error) {
          reject(error);
        } finally {
          fileInput.remove();
        }
      };
      fileInput.onabort = () => {
        reject('文件上传被中止');
      };
      fileInput.onerror = (e) => {
        reject(e);
      };
      fileInput.oncancel = () => {
        reject('文件上传被取消');
      };
      fileInput.click();
    });
  }
}

