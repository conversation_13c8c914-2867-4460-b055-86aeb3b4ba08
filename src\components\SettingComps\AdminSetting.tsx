import { FormLayout } from "@formily/antd-v5";
import { FormProvider } from "@formily/react";
import { SchemaField, useForm } from "@/components/Formily";
import { useModel } from 'foca';
import { settingModel } from '@/stores/settingModel';
import { useEffect } from "react";

const AdminSetting = () => {
  const form = useForm({});
  const settingModelState = useModel(settingModel);

  useEffect(() => {
    form.setInitialValues(settingModelState.Admin)
    form.setValues(settingModelState.Admin)
  }, [settingModelState.Admin])

  return (
    <FormProvider form={form}>
      <FormLayout layout="vertical" bordered>
        <SchemaField>
          {/* <SchemaField.String
            name="deletePassword"
            title="删除密码"
            x-decorator="EditFormItem"
            x-component="Password"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '用于后台删除操作的密码',
              parentKey: 'Admin',
            }}
          /> */}
          <SchemaField.String
            name="beforeTradingAmountDate"
            title="交易金额统计日期"
            x-decorator="EditFormItem"
            x-component="DatePicker"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '用于大屏展示年交易额统计',
              parentKey: 'Admin',
            }}
          />
          <SchemaField.Number
            name="beforeTradingAmountTotal"
            title="交易金额统计金额"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '用于大屏展示年交易额统计',
              parentKey: 'Admin',
            }}
          />

          <SchemaField.Array
            name="plantingAreaVideos"
            title="产地监控"
            x-component="ArrayItems"
            x-decorator="EditFormItem"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '产地监控地址',
              parentKey: 'Admin',
            }}
          >
            <SchemaField.Void x-component="Space">
              <SchemaField.Void
                x-decorator="FormItem"
                x-component="ArrayItems.SortHandle"
              />
              <SchemaField.String
                x-decorator="FormItem"
                required
                name="input"
                x-component="Input"
                x-component-props={{
                  style: {
                    width: 600,
                  }
                }}
              />
              <SchemaField.Void
                x-decorator="FormItem"
                x-component="ArrayItems.Remove"
              />
              <SchemaField.Void
                x-decorator="FormItem"
                x-component="ArrayItems.Copy"
              />
            </SchemaField.Void>
            <SchemaField.Void
              x-component="ArrayItems.Addition"
              title="添加条目"
            />
          </SchemaField.Array>
          <SchemaField.String
            name="buildingNvrAddress"
            title="场馆监控NVR地址"
            x-decorator="EditFormItem"
            x-component="Input"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '格式是ip:port',
              parentKey: 'Admin',
            }}
          />
          <SchemaField.Array
            name="buildingNvrLiveRange"
            title="场馆监控直播时间"
            x-decorator="EditFormItem"
            x-component="TimePicker.RangePicker"
            x-component-props={{
              format: 'HH:mm'
            }}
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '此时间段内直播，其他时间复播',
              parentKey: 'Admin',
            }}
          />

          <SchemaField.Number
            name="peopleCountRate"
            title="人数统计系数"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-decorator-props={{
              tooltip: '用于大屏人数统计修正',
              parentKey: 'Admin',
            }}
          />



        </SchemaField>


      </FormLayout>
    </FormProvider>
  );
}
export default AdminSetting;