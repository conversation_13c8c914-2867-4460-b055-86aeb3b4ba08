import { defineConfig } from 'vite';
// import { fileURLToPath, URL } from "url";
import path from 'path';
import React from '@vitejs/plugin-react-swc';
import UnoCSS from 'unocss/vite';
import { analyzer } from 'vite-bundle-analyzer';
import { viteExternalsPlugin } from 'vite-plugin-externals';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  return {
    build: {
      // sourcemap: true,
      chunkSizeWarningLimit: 1000,
    },
    plugins: [
      // viteExternalsPlugin(
      //   {
      //     echarts: 'echarts',
      //   },
      //   { disableInServe: true }
      // ),
      viteExternalsPlugin(
        {
          pdfLib: 'pdf-lib',
        },
        { disableInServe: true }
      ),
      
      React(),
      UnoCSS(),
      mode === 'analyze' ? analyzer() : undefined,
    ],
    server: {
      host: true,
      port: 8081,
      proxy: {
        // // '/admin/ws': {
        // //   target: 'http://127.0.0.1:8888',
        // //   // target: 'https://api-dev.dnhhsj.dounanflowers.com',
        // //   changeOrigin: true,
        // // },
        // '/admin': {
        //   target: 'http://127.0.0.1:8888',
        //   // target: 'https://api-dev.dnhhsj.dounanflowers.com',
        //   changeOrigin: true,
        // },
      
      }
    },
    resolve: {
      alias: {
        // '@/': fileURLToPath(new URL('./src/', import.meta.url)),
        '@': path.resolve(__dirname, './src/'),
      },
    },
  };
});
