import { apiBillUpdate } from '@/apis/apis';
import { useFormModal } from '@/components/FormilyModal';
import { useMemoizedFn } from 'ahooks';
import { App } from 'antd';

interface TBillRemarkFormData {
  remark: string;
}

export const useBillRemarkFormModal = (cb: () => void) => {
  const { message } = App.useApp();
  const { formModalRef, formModalHolder } = useFormModal<TBillRemarkFormData>();

  const showEditModal = useMemoizedFn(async (record: TBill) => {
    formModalRef.current?.show({
      modalTitle: '编辑账单备注',
      initialValues: {
        remark: record.remark || '',
      },
      modalWidth: 600,
      onAutoSubmit: async (values: TBillRemarkFormData) => {
        await apiBillUpdate({
          ...values,
          id: record.id,
        });
        message.success("更新成功");
        cb();
      },
      schema: {
        type: "object",
        properties: {
          remark: {
            title: "备注",
            type: "string",
            "x-decorator": "FormItem",
            "x-component": "Input.TextArea",
            "x-component-props": {
              rows: 4
            }
          },
        },
      },
    });
  });

  return {
    showEditModal,
    formModalHolder,
  };
};
