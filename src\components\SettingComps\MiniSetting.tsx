import { FormLayout } from "@formily/antd-v5";
import { FormProvider } from "@formily/react";
import { SchemaField, useForm } from "@/components/Formily";
import { useModel } from 'foca';
import { settingModel } from '@/stores/settingModel';
import { useEffect } from "react";
import { AgreeDicts } from "@/utils/agreeDict";

const MiniSetting = () => {
  const form = useForm({});
  const settingModelState = useModel(settingModel);

  useEffect(() => {
    form.setInitialValues(settingModelState.Mini)
    form.setValues(settingModelState.Mini)
  }, [settingModelState.Mini])

  return (
    <FormProvider form={form}>
      <FormLayout layout="vertical" bordered>
        <SchemaField>
          <SchemaField.String
            name="serviceTel"
            title="服务电话"
            x-decorator="EditFormItem"
            x-component="Input"
            x-pattern="readPretty"
            x-decorator-props={{
              // tooltip: '',
              parentKey: 'Mini',
            }}
            x-validator={[]}
          />
          <SchemaField.String
            name="artworkServiceTel"
            title="艺术展品服务电话"
            x-decorator="EditFormItem"
            x-component="Input"
            x-pattern="readPretty"
            x-decorator-props={{
              // tooltip: '',
              parentKey: 'Mini',
            }}
            x-validator={[]}
          />
          {/* <SchemaField.Number
            name="auctionTailCycleDays"
            title="拍卖尾款支付周期"
            x-decorator="EditFormItem"
            x-component="NumberPicker"
            x-pattern="readPretty"
            x-decorator-props={{
              // tooltip: '',
              parentKey: 'Mini',
            }}
            x-component-props={{
              addonAfter: '天',
            }}
            x-validator={[]}
          /> */}

          <SchemaField.Array
            name="homeJingangIcons"
            title="首页金刚区图标"
            x-decorator="EditFormItem"
            x-component="NavIcons"
            x-pattern="readPretty"
            x-decorator-props={{
              parentKey: 'Mini',
            }}
            x-component-props={{
              cols: 4,
            } as any}
          />

          <SchemaField.Array
            name="homeNavIcons"
            title="首页图标导航"
            x-decorator="EditFormItem"
            x-component="NavIcons"
            x-pattern="readPretty"
            x-decorator-props={{
              parentKey: 'Mini',
            }}
            x-component-props={{
              cols: 5,
            } as any}
          />
          <SchemaField.Array
            name="storeNavIcons"
            title="玩转斗南图标导航"
            x-decorator="EditFormItem"
            x-component="NavIcons"
            x-pattern="readPretty"
            x-decorator-props={{
              parentKey: 'Mini',
            }}
            x-component-props={{
              cols: 4,
              typeOptions: AgreeDicts.Product_type.options,
              editFields: ['icon', 'label', 'type', 'hide'],
            } as any}
          />
          {/* <SchemaField.Array
            name="storeNavTypes"
            title="商品类型导航"
            x-decorator="EditFormItem"
            x-component="Checkbox.Group"
            x-pattern="readPretty"
            enum={AgreeDicts.Product_type.options}
            x-decorator-props={{
              parentKey: 'Mini',
            }}
            x-component-props={{
              // cols: 4,
            } as any}
          /> */}

        </SchemaField>
      </FormLayout>
    </FormProvider>
  );
}
export default MiniSetting;